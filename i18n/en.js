"use strict";

module.exports = {
    description: "Professional ECS Framework Development Assistant: One-click installation of @esengine/ecs-framework, intelligent code generator for quick creation of components and systems, project template generation, real-time status detection and version management. Provides welcome panel, debug panel, code generator and behavior tree AI component library to make ECS development in Cocos Creator more efficient and convenient.",
    
    open_panel: "Default Panel",
    send_to_panel: "Send message to panel",
    
    menu: {
        panel: "Panel",
        develop: "Develop", 
        create: "Create",
        open: "Open"
    }
};