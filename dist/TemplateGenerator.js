"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateGenerator = void 0;
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
/**
 * ECS启动模板生成器
 * 生成最基础的ECS框架启动模板，不包含业务逻辑
 */
class TemplateGenerator {
    constructor(projectPath) {
        this.projectPath = projectPath;
        this.ecsDir = path.join(projectPath, 'assets', 'scripts', 'ecs');
    }
    /**
     * 检查是否已经存在ECS模板
     */
    checkTemplateExists() {
        return fs.existsSync(this.ecsDir);
    }
    /**
     * 获取已存在的文件列表
     */
    getExistingFiles() {
        if (!this.checkTemplateExists())
            return [];
        const files = [];
        this.scanDirectory(this.ecsDir, '', files);
        return files;
    }
    scanDirectory(dirPath, relativePath, files) {
        if (!fs.existsSync(dirPath))
            return;
        const items = fs.readdirSync(dirPath);
        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            const relativeFilePath = relativePath ? `${relativePath}/${item}` : item;
            if (fs.statSync(fullPath).isDirectory()) {
                this.scanDirectory(fullPath, relativeFilePath, files);
            }
            else {
                files.push(relativeFilePath);
            }
        }
    }
    /**
     * 删除现有的ECS模板
     */
    removeExistingTemplate() {
        if (fs.existsSync(this.ecsDir)) {
            fs.rmSync(this.ecsDir, { recursive: true, force: true });
            console.log('Removed existing ECS template');
        }
    }
    /**
     * 创建ECS启动模板
     */
    createTemplate() {
        // 创建目录结构
        this.createDirectories();
        // 创建ECS启动管理器
        this.createECSManager();
        // 创建基础游戏场景
        this.createBaseGameScene();
        // 创建README文档
        this.createReadme();
        console.log('ECS启动模板创建成功');
    }
    /**
     * 创建目录结构
     */
    createDirectories() {
        const dirs = [
            this.ecsDir,
            path.join(this.ecsDir, 'scenes'),
            path.join(this.ecsDir, 'components'),
            path.join(this.ecsDir, 'systems')
        ];
        dirs.forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                console.log(`Created directory: ${path.relative(this.projectPath, dir)}`);
            }
        });
    }
    /**
     * 创建ECS管理器
     */
    createECSManager() {
        this.writeFile(path.join(this.ecsDir, 'ECSManager.ts'), `import { Core } from '@esengine/ecs-framework';
import { Component, _decorator } from 'cc';
import { GameScene } from './scenes/GameScene';

const { ccclass, property } = _decorator;

/**
 * ECS管理器 - Cocos Creator组件
 * 将此组件添加到场景中的任意节点上即可启动ECS框架
 * 
 * 使用说明：
 * 1. 在Cocos Creator场景中创建一个空节点
 * 2. 将此ECSManager组件添加到该节点
 * 3. 运行场景即可自动启动ECS框架
 */
@ccclass('ECSManager')
export class ECSManager extends Component {
    
    @property({
        tooltip: '是否启用调试模式（建议开发阶段开启）'
    })
    public debugMode: boolean = true;
    
    private isInitialized: boolean = false;
    
    /**
     * 组件启动时初始化ECS
     */
    start() {
        this.initializeECS();
    }
    
    /**
     * 初始化ECS框架
     */
    private initializeECS(): void {
        if (this.isInitialized) return;
        
        console.log('🎮 正在初始化ECS框架...');
        
        try {
            // 1. 创建Core实例，启用调试功能
            if (this.debugMode) {
                Core.create({
                    debugConfig: {
                        enabled: true,
                        websocketUrl: 'ws://localhost:8080/ecs-debug',
                        autoReconnect: true,
                        updateInterval: 100,
                        channels: {
                            entities: true,
                            systems: true,
                            performance: true,
                            components: true,
                            scenes: true
                        }
                    }
                });
                console.log('🔧 ECS调试模式已启用，可在Cocos Creator扩展面板中查看调试信息');
            } else {
                Core.create(false);
            }
            
            // 2. 创建游戏场景
            const gameScene = new GameScene();
            
            // 3. 设置为当前场景（会自动调用scene.begin()）
            Core.scene = gameScene;
            
            this.isInitialized = true;
            console.log('✅ ECS框架初始化成功！');
            console.log('📖 请查看 assets/scripts/ecs/README.md 了解如何添加组件和系统');
            
        } catch (error) {
            console.error('❌ ECS框架初始化失败:', error);
        }
    }
    
    /**
     * 每帧更新ECS框架
     */
    update(deltaTime: number) {
        if (this.isInitialized) {
            // 更新ECS核心系统
            Core.update(deltaTime);
        }
    }
    
    /**
     * 组件销毁时清理ECS
     */
    onDestroy() {
        if (this.isInitialized) {
            console.log('🧹 清理ECS框架...');
            // ECS框架会自动处理场景清理
            this.isInitialized = false;
        }
    }
}
`);
    }
    /**
     * 创建基础游戏场景
     */
    createBaseGameScene() {
        this.writeFile(path.join(this.ecsDir, 'scenes', 'GameScene.ts'), `import { Scene } from '@esengine/ecs-framework';

/**
 * 游戏场景
 * 
 * 这是您的主游戏场景。在这里可以：
 * - 添加游戏系统
 * - 创建初始实体
 * - 设置场景参数
 */
export class GameScene extends Scene {
    
    /**
     * 场景初始化
     * 在场景创建时调用，用于设置基础配置
     */
    public initialize(): void {
        super.initialize();
        
        // 设置场景名称
        this.name = "MainGameScene";
        
        console.log('🎯 游戏场景已创建');
        
        // TODO: 在这里添加您的游戏系统
        // 例如：this.addEntityProcessor(new MovementSystem());
        
        // TODO: 在这里创建初始实体
        // 例如：this.createEntity("Player");
    }
    
    /**
     * 场景开始运行
     * 在场景开始时调用，用于执行启动逻辑
     */
    public onStart(): void {
        super.onStart();
        
        console.log('🚀 游戏场景已启动');
        
        // TODO: 在这里添加场景启动逻辑
        // 例如：创建UI、播放音乐、初始化游戏状态等
    }
    
    /**
     * 场景卸载
     * 在场景结束时调用，用于清理资源
     */
    public unload(): void {
        console.log('🛑 游戏场景已结束');
        
        // TODO: 在这里添加清理逻辑
        // 例如：清理缓存、释放资源等
        
        super.unload();
    }
}
`);
    }
    /**
     * 创建README文档
     */
    createReadme() {
        this.writeFile(path.join(this.ecsDir, 'README.md'), `# ECS框架启动模板

欢迎使用ECS框架！这是一个最基础的启动模板，帮助您快速开始ECS项目开发。

## 📁 项目结构

\`\`\`
ecs/
├── components/          # 组件目录（请在此添加您的组件）
├── systems/            # 系统目录（请在此添加您的系统）
├── scenes/             # 场景目录
│   └── GameScene.ts    # 主游戏场景
├── ECSManager.ts       # ECS管理器组件
└── README.md          # 本文档
\`\`\`

## 🚀 快速开始

### 1. 启动ECS框架

ECS框架已经配置完成！您只需要：

1. 在Cocos Creator中打开您的场景
2. 创建一个空节点（例如命名为"ECSManager"）
3. 将 \`ECSManager\` 组件添加到该节点
4. 运行场景，ECS框架将自动启动

### 2. 查看控制台输出

如果一切正常，您将在控制台看到：

\`\`\`
🎮 正在初始化ECS框架...
🔧 ECS调试模式已启用，可在Cocos Creator扩展面板中查看调试信息
🎯 游戏场景已创建
✅ ECS框架初始化成功！
🚀 游戏场景已启动
\`\`\`

### 3. 使用调试面板

ECS框架已启用调试功能，您可以：

1. 在Cocos Creator编辑器菜单中选择 "扩展" → "ECS Framework" → "调试面板"
2. 调试面板将显示实时的ECS运行状态：
   - 实体数量和状态
   - 系统执行信息
   - 性能监控数据
   - 组件统计信息

**注意**：调试功能会消耗一定性能，正式发布时建议关闭调试模式。

## 📚 下一步开发

### 创建您的第一个组件

在 \`components/\` 目录下创建组件：

\`\`\`typescript
// components/PositionComponent.ts
import { Component } from '@esengine/ecs-framework';
import { Vec3 } from 'cc';

export class PositionComponent extends Component {
    public position: Vec3 = new Vec3();
    
    constructor(x: number = 0, y: number = 0, z: number = 0) {
        super();
        this.position.set(x, y, z);
    }
}
\`\`\`

### 创建您的第一个系统

在 \`systems/\` 目录下创建系统：

\`\`\`typescript
// systems/MovementSystem.ts
import { EntitySystem, Entity, Matcher } from '@esengine/ecs-framework';
import { PositionComponent } from '../components/PositionComponent';

export class MovementSystem extends EntitySystem {
    constructor() {
        super(Matcher.empty().all(PositionComponent));
    }
    
    protected process(entities: Entity[]): void {
        for (const entity of entities) {
            const position = entity.getComponent(PositionComponent);
            if (position) {
                // TODO: 在这里编写移动逻辑
                console.log(\`实体 \${entity.name} 位置: \${position.position}\`);
            }
        }
    }
}
\`\`\`

### 在场景中注册系统

在 \`scenes/GameScene.ts\` 的 \`initialize()\` 方法中添加：

\`\`\`typescript
import { MovementSystem } from '../systems/MovementSystem';

public initialize(): void {
    super.initialize();
    this.name = "MainGameScene";
    
    // 添加系统
    this.addEntityProcessor(new MovementSystem());
    
    // 创建测试实体
    const testEntity = this.createEntity("TestEntity");
    testEntity.addComponent(new PositionComponent(0, 0, 0));
}
\`\`\`

## 🔗 学习资源

- [ECS框架完整文档](https://github.com/esengine/ecs-framework)
- [ECS概念详解](https://github.com/esengine/ecs-framework/blob/master/docs/concepts-explained.md)
- [新手教程](https://github.com/esengine/ecs-framework/blob/master/docs/beginner-tutorials.md)
- [组件设计指南](https://github.com/esengine/ecs-framework/blob/master/docs/component-design-guide.md)
- [系统开发指南](https://github.com/esengine/ecs-framework/blob/master/docs/system-guide.md)

## 💡 开发提示

1. **组件只存储数据**：避免在组件中编写复杂逻辑
2. **系统处理逻辑**：所有业务逻辑应该在系统中实现
3. **使用Matcher过滤实体**：系统通过Matcher指定需要处理的实体类型
4. **性能优化**：大量实体时考虑使用位掩码查询和组件索引

## ❓ 常见问题

### Q: 如何创建实体？
A: 在场景中使用 \`this.createEntity("实体名称")\`

### Q: 如何给实体添加组件？
A: 使用 \`entity.addComponent(new YourComponent())\`

### Q: 如何获取实体的组件？
A: 使用 \`entity.getComponent(YourComponent)\`

### Q: 如何删除实体？
A: 使用 \`entity.destroy()\` 或 \`this.destroyEntity(entity)\`

---

🎮 **开始您的ECS开发之旅吧！**

如有问题，请查阅官方文档或提交Issue。
`);
    }
    /**
     * 写入文件
     */
    writeFile(filePath, content) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`Created file: ${path.relative(this.projectPath, filePath)}`);
    }
}
exports.TemplateGenerator = TemplateGenerator;
//# sourceMappingURL=data:application/json;base64,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