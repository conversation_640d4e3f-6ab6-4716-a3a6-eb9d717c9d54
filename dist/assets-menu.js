"use strict";
function onAssetMenu(assetInfo) {
    console.log('[AssetMenu] onAssetMenu 被调用，资源信息:', assetInfo);
    console.log('[AssetMenu] assetInfo 完整结构:', JSON.stringify(assetInfo, null, 2));
    const menuItems = [];
    // 如果是在 scripts/ecs/components 目录下，添加创建组件的选项
    if (assetInfo && assetInfo.isDirectory && assetInfo.file) {
        const filePath = assetInfo.file.replace(/\\/g, '/');
        // 检查是否在 ECS 相关目录
        if (filePath.includes('scripts/ecs/components')) {
            menuItems.push({
                label: '创建 ECS 组件',
                click: () => {
                    // 发送消息到主进程创建组件
                    Editor.Message.request('cocos-ecs-extension', 'create-ecs-component', assetInfo);
                }
            });
        }
        if (filePath.includes('scripts/ecs/systems')) {
            menuItems.push({
                label: '创建 ECS 系统',
                click: () => {
                    // 发送消息到主进程创建系统
                    Editor.Message.request('cocos-ecs-extension', 'create-ecs-system', assetInfo);
                }
            });
        }
        if (filePath.includes('scripts/ecs')) {
            menuItems.push({
                label: 'ECS 代码生成器',
                click: () => {
                    // 打开ECS代码生成器面板
                    Editor.Panel.open('cocos-ecs-extension.generator');
                }
            });
        }
    }
    // 如果是 TypeScript 文件，添加 ECS 相关工具选项
    if (assetInfo && assetInfo.file && assetInfo.file.endsWith('.ts')) {
        const filePath = assetInfo.file.replace(/\\/g, '/');
        if (filePath.includes('scripts/ecs/')) {
            menuItems.push({
                label: 'ECS 工具',
                submenu: [
                    {
                        label: '重新生成模板代码',
                        click: () => {
                            Editor.Message.request('cocos-ecs-extension', 'regenerate-template', assetInfo);
                        }
                    },
                    {
                        label: '添加到 ECS 管理器',
                        click: () => {
                            Editor.Message.request('cocos-ecs-extension', 'add-to-manager', assetInfo);
                        }
                    }
                ]
            });
        }
    }
    console.log('[AssetMenu] 返回菜单项数量:', menuItems.length);
    return menuItems;
}
// CommonJS 导出
module.exports = {
    onAssetMenu
};
//# sourceMappingURL=data:application/json;base64,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