"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PanelHandler = void 0;
/**
 * 面板管理相关的处理器
 */
class PanelHandler {
    /**
     * 打开默认面板
     */
    static openDefaultPanel() {
        try {
            Editor.Panel.open('cocos-ecs-extension');
            console.log('Default panel opened successfully');
        }
        catch (error) {
            console.error('Failed to open default panel:', error);
            Editor.Dialog.error('打开面板失败', {
                detail: `无法打开面板：\n\n${error}\n\n请尝试重启Cocos Creator编辑器。`,
            });
        }
    }
    /**
     * 打开调试面板
     */
    static openDebugPanel() {
        try {
            Editor.Panel.open('cocos-ecs-extension.debug');
            console.log('Debug panel opened successfully');
        }
        catch (error) {
            console.error('Failed to open debug panel:', error);
            Editor.Dialog.error('打开调试面板失败', {
                detail: `无法打开调试面板：\n\n${error}\n\n请尝试重启Cocos Creator编辑器。`,
            });
        }
    }
    /**
     * 打开代码生成器面板
     */
    static openGeneratorPanel() {
        try {
            Editor.Panel.open('cocos-ecs-extension.generator');
            console.log('Generator panel opened successfully');
        }
        catch (error) {
            console.error('Failed to open generator panel:', error);
            Editor.Dialog.error('打开代码生成器失败', {
                detail: `无法打开代码生成器面板：\n\n${error}\n\n请尝试重启Cocos Creator编辑器。`,
            });
        }
    }
    /**
     * 打开文档面板
     */
    static openDocsPanel() {
        try {
            Editor.Panel.open('cocos-ecs-extension.docs');
            console.log('Docs panel opened successfully');
        }
        catch (error) {
            console.error('Failed to open docs panel:', error);
            Editor.Dialog.error('打开文档面板失败', {
                detail: `无法打开文档面板：\n\n${error}\n\n请尝试重启Cocos Creator编辑器。`,
            });
        }
    }
    /**
     * 打开行为树面板
     */
    static openBehaviorTreePanel() {
        try {
            Editor.Panel.open('cocos-ecs-extension.behavior-tree');
            console.log('Behavior Tree panel opened successfully');
        }
        catch (error) {
            console.error('Failed to open behavior tree panel:', error);
            Editor.Dialog.error('打开行为树面板失败', {
                detail: `无法打开行为树AI组件库面板：\n\n${error}\n\n请尝试重启Cocos Creator编辑器。`,
            });
        }
    }
}
exports.PanelHandler = PanelHandler;
//# sourceMappingURL=data:application/json;base64,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