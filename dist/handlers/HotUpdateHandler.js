"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.HotUpdateHandler = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const crypto = __importStar(require("crypto"));
/**
 * 热更新处理器
 */
class HotUpdateHandler {
    /**
     * 初始化热更新系统
     */
    static async initialize() {
        console.log('[HotUpdate] 初始化热更新系统...');
        try {
            await this.loadConfig();
            await this.startAutoCheck();
            console.log('[HotUpdate] 热更新系统初始化完成');
        }
        catch (error) {
            console.error('[HotUpdate] 初始化失败:', error);
        }
    }
    /**
     * 加载配置
     */
    static async loadConfig() {
        const configPath = path.join(this.EXTENSION_PATH, this.CONFIG_FILE);
        try {
            if (await fs.pathExists(configPath)) {
                this.config = await fs.readJSON(configPath);
            }
            else {
                // 创建默认配置
                this.config = {
                    serverUrl: 'https://earthonline-game.cn/api/plugin-updates',
                    currentVersion: this.getCurrentVersion(),
                    updateChannel: 'stable',
                    autoCheck: true,
                    checkInterval: 60 // 60分钟检查一次
                };
                await this.saveConfig();
            }
        }
        catch (error) {
            console.error('[HotUpdate] 配置加载失败:', error);
            throw error;
        }
    }
    /**
     * 保存配置
     */
    static async saveConfig() {
        const configPath = path.join(this.EXTENSION_PATH, this.CONFIG_FILE);
        await fs.writeJSON(configPath, this.config, { spaces: 2 });
    }
    /**
     * 获取当前版本
     */
    static getCurrentVersion() {
        try {
            const packagePath = path.join(this.EXTENSION_PATH, 'package.json');
            const packageInfo = fs.readJSONSync(packagePath);
            return packageInfo.version;
        }
        catch (error) {
            console.error('[HotUpdate] 无法获取当前版本:', error);
            return '1.0.0';
        }
    }
    /**
     * 开始自动检查
     */
    static async startAutoCheck() {
        if (!this.config.autoCheck) {
            return;
        }
        // 立即检查一次
        await this.checkForUpdates(true);
        // 设置定时检查
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
        }
        this.updateTimer = setInterval(async () => {
            await this.checkForUpdates(true);
        }, this.config.checkInterval * 60 * 1000);
    }
    /**
     * 检查更新
     */
    static async checkForUpdates(silent = false) {
        console.log('[HotUpdate] 检查更新中...');
        try {
            const response = await fetch(`${this.config.serverUrl}/check`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    currentVersion: this.config.currentVersion,
                    pluginId: 'cocos-ecs-extension', // 当前插件ID
                    channel: this.config.updateChannel,
                    platform: process.platform,
                    editorVersion: Editor.App.version
                })
            });
            if (!response.ok) {
                throw new Error(`服务器响应错误: ${response.status}`);
            }
            const versionInfo = await response.json();
            if (this.isNewerVersion(versionInfo.version, this.config.currentVersion)) {
                console.log(`[HotUpdate] 发现新版本: ${versionInfo.version}`);
                if (!silent) {
                    await this.showUpdateDialog(versionInfo);
                }
                return versionInfo;
            }
            else {
                if (!silent) {
                    Editor.Dialog.info('检查更新', {
                        detail: '当前已是最新版本!'
                    });
                }
                return null;
            }
        }
        catch (error) {
            console.error('[HotUpdate] 检查更新失败:', error);
            if (!silent) {
                Editor.Dialog.error('检查更新失败', {
                    detail: `无法连接到更新服务器：\n\n${error instanceof Error ? error.message : String(error)}`
                });
            }
            return null;
        }
    }
    /**
     * 比较版本号
     */
    static isNewerVersion(newVersion, currentVersion) {
        const parseVersion = (version) => {
            return version.split('.').map(Number);
        };
        const newParts = parseVersion(newVersion);
        const currentParts = parseVersion(currentVersion);
        const maxLength = Math.max(newParts.length, currentParts.length);
        for (let i = 0; i < maxLength; i++) {
            const newPart = newParts[i] || 0;
            const currentPart = currentParts[i] || 0;
            if (newPart > currentPart)
                return true;
            if (newPart < currentPart)
                return false;
        }
        return false;
    }
    /**
     * 显示更新对话框
     */
    static async showUpdateDialog(versionInfo) {
        const message = `发现新版本 ${versionInfo.version}!\n\n` +
            `发布时间: ${versionInfo.releaseDate}\n\n` +
            `更新内容:\n${versionInfo.description}\n\n` +
            `文件大小: ${this.formatFileSize(versionInfo.fileSize)}`;
        const buttons = versionInfo.mandatory ? ['立即更新'] : ['立即更新', '稍后提醒', '跳过此版本'];
        const result = await Editor.Dialog.info('插件更新', {
            detail: message,
            buttons: buttons
        });
        switch (result.response) {
            case 0: // 立即更新
                await this.downloadAndInstallUpdate(versionInfo);
                break;
            case 1: // 稍后提醒
                if (!versionInfo.mandatory) {
                    console.log('[HotUpdate] 用户选择稍后更新');
                }
                break;
            case 2: // 跳过此版本
                if (!versionInfo.mandatory) {
                    await this.skipVersion(versionInfo.version);
                }
                break;
        }
    }
    /**
     * 下载并安装更新
     */
    static async downloadAndInstallUpdate(versionInfo) {
        console.log(`[HotUpdate] 开始下载更新: ${versionInfo.version}`);
        try {
            // 显示进度对话框
            const progressDialog = this.showProgressDialog('正在下载更新...');
            // 下载更新包
            const updatePath = await this.downloadUpdate(versionInfo, (progress) => {
                // 更新进度
                console.log(`[HotUpdate] 下载进度: ${progress}%`);
            });
            progressDialog.detail = '正在验证文件...';
            // 验证文件完整性
            const isValid = await this.verifyUpdate(updatePath, versionInfo.checksum);
            if (!isValid) {
                throw new Error('文件校验失败，更新包可能已损坏');
            }
            progressDialog.detail = '正在安装更新...';
            // 安装更新
            await this.installUpdate(updatePath, versionInfo);
            // 更新版本信息
            this.config.currentVersion = versionInfo.version;
            await this.saveConfig();
            // 显示安装完成对话框
            const result = await Editor.Dialog.info('更新完成', {
                detail: `插件已成功更新到版本 ${versionInfo.version}!\n\n为了使更新生效，需要重启Cocos Creator编辑器。`,
                buttons: ['立即重启', '稍后重启']
            });
            if (result.response === 0) {
                this.restartEditor();
            }
        }
        catch (error) {
            console.error('[HotUpdate] 更新失败:', error);
            Editor.Dialog.error('更新失败', {
                detail: `更新过程中发生错误：\n\n${error instanceof Error ? error.message : String(error)}`
            });
        }
    }
    /**
     * 下载更新
     */
    static async downloadUpdate(versionInfo, onProgress) {
        var _a;
        const response = await fetch(versionInfo.downloadUrl);
        if (!response.ok) {
            throw new Error(`下载失败: ${response.status}`);
        }
        const totalSize = parseInt(response.headers.get('content-length') || '0');
        let downloadedSize = 0;
        const tempPath = path.join(this.EXTENSION_PATH, 'temp', `update-${versionInfo.version}.zip`);
        await fs.ensureDir(path.dirname(tempPath));
        const writer = fs.createWriteStream(tempPath);
        const reader = (_a = response.body) === null || _a === void 0 ? void 0 : _a.getReader();
        if (!reader) {
            throw new Error('无法创建下载流');
        }
        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done)
                    break;
                writer.write(value);
                downloadedSize += value.length;
                if (onProgress && totalSize > 0) {
                    const progress = Math.round((downloadedSize / totalSize) * 100);
                    onProgress(progress);
                }
            }
            writer.end();
            return tempPath;
        }
        catch (error) {
            writer.destroy();
            await fs.remove(tempPath).catch(() => { }); // 忽略删除错误
            throw error;
        }
    }
    /**
     * 验证更新包
     */
    static async verifyUpdate(filePath, expectedChecksum) {
        try {
            const fileBuffer = await fs.readFile(filePath);
            const hash = crypto.createHash('sha256');
            hash.update(fileBuffer);
            const actualChecksum = hash.digest('hex');
            return actualChecksum === expectedChecksum;
        }
        catch (error) {
            console.error('[HotUpdate] 文件校验失败:', error);
            return false;
        }
    }
    /**
     * 安装更新
     */
    static async installUpdate(updatePath, versionInfo) {
        const extractPath = path.join(this.EXTENSION_PATH, 'temp', 'extract');
        try {
            // 清理临时目录
            await fs.remove(extractPath);
            await fs.ensureDir(extractPath);
            // 解压更新包
            await this.extractZip(updatePath, extractPath);
            // 备份当前版本
            const backupPath = path.join(this.EXTENSION_PATH, 'backup', this.config.currentVersion);
            await this.createBackup(backupPath);
            // 应用更新文件
            await this.applyUpdateFiles(extractPath, versionInfo.files);
            // 清理临时文件
            await fs.remove(path.dirname(updatePath));
        }
        catch (error) {
            console.error('[HotUpdate] 安装更新失败:', error);
            // 尝试恢复备份
            await this.restoreBackup();
            throw error;
        }
    }
    /**
     * 解压ZIP文件
     */
    static async extractZip(zipPath, extractPath) {
        return new Promise((resolve, reject) => {
            // 这里使用node的解压库，您可能需要安装 yauzl 或 adm-zip
            const AdmZip = require('adm-zip');
            try {
                const zip = new AdmZip(zipPath);
                zip.extractAllTo(extractPath, true);
                resolve();
            }
            catch (error) {
                reject(error);
            }
        });
    }
    /**
     * 应用更新文件
     */
    static async applyUpdateFiles(extractPath, files) {
        for (const file of files) {
            const sourcePath = path.join(extractPath, file.path);
            const targetPath = path.join(this.EXTENSION_PATH, file.path);
            try {
                switch (file.action) {
                    case 'add':
                    case 'update':
                        await fs.ensureDir(path.dirname(targetPath));
                        await fs.copy(sourcePath, targetPath, { overwrite: true });
                        break;
                    case 'delete':
                        await fs.remove(targetPath);
                        break;
                }
            }
            catch (error) {
                console.error(`[HotUpdate] 处理文件失败 ${file.path}:`, error);
                throw error;
            }
        }
    }
    /**
     * 创建备份
     */
    static async createBackup(backupPath) {
        await fs.ensureDir(backupPath);
        const sourceFiles = [
            'source',
            'static',
            'package.json',
            'README.md'
        ];
        for (const file of sourceFiles) {
            const sourcePath = path.join(this.EXTENSION_PATH, file);
            const targetPath = path.join(backupPath, file);
            if (await fs.pathExists(sourcePath)) {
                await fs.copy(sourcePath, targetPath);
            }
        }
    }
    /**
     * 恢复备份
     */
    static async restoreBackup() {
        const backupPath = path.join(this.EXTENSION_PATH, 'backup', this.config.currentVersion);
        if (await fs.pathExists(backupPath)) {
            console.log('[HotUpdate] 正在恢复备份...');
            const backupFiles = await fs.readdir(backupPath);
            for (const file of backupFiles) {
                const sourcePath = path.join(backupPath, file);
                const targetPath = path.join(this.EXTENSION_PATH, file);
                await fs.copy(sourcePath, targetPath, { overwrite: true });
            }
        }
    }
    /**
     * 跳过版本
     */
    static async skipVersion(version) {
        const skipPath = path.join(this.EXTENSION_PATH, 'skipped-versions.json');
        let skippedVersions = [];
        if (await fs.pathExists(skipPath)) {
            skippedVersions = await fs.readJSON(skipPath);
        }
        if (!skippedVersions.includes(version)) {
            skippedVersions.push(version);
            await fs.writeJSON(skipPath, skippedVersions);
        }
    }
    /**
     * 显示进度对话框
     */
    static showProgressDialog(message) {
        // 这是一个简化版本，实际可能需要创建自定义进度条
        return {
            detail: message
        };
    }
    /**
     * 格式化文件大小
     */
    static formatFileSize(bytes) {
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        return `${size.toFixed(1)} ${units[unitIndex]}`;
    }
    /**
     * 重启编辑器
     */
    static restartEditor() {
        // 注意：这个功能需要特殊权限，可能需要用户手动重启
        console.log('[HotUpdate] 请求重启编辑器');
        try {
            // 尝试重启编辑器（可能不会成功）
            Editor.App.quit();
        }
        catch (error) {
            console.warn('[HotUpdate] 无法自动重启编辑器:', error);
        }
    }
    /**
     * 设置更新配置
     */
    static async setConfig(newConfig) {
        this.config = Object.assign(Object.assign({}, this.config), newConfig);
        await this.saveConfig();
        // 重新启动自动检查
        if (this.config.autoCheck) {
            await this.startAutoCheck();
        }
        else if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
        }
    }
    /**
     * 获取配置
     */
    static getConfig() {
        return Object.assign({}, this.config);
    }
    /**
     * 清理资源
     */
    static cleanup() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
        }
    }
}
exports.HotUpdateHandler = HotUpdateHandler;
HotUpdateHandler.CONFIG_FILE = 'hot-update-config.json';
HotUpdateHandler.VERSION_FILE = 'version-info.json';
HotUpdateHandler.EXTENSION_PATH = Editor.Package.getPath('cocos-ecs-extension') || '';
HotUpdateHandler.updateTimer = null;
//# sourceMappingURL=data:application/json;base64,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