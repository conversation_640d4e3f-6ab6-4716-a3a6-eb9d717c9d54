"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.EcsFrameworkHandler = void 0;
const child_process_1 = require("child_process");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const TemplateGenerator_1 = require("../TemplateGenerator");
/**
 * ECS框架相关的处理器
 */
class EcsFrameworkHandler {
    /**
     * 安装ECS Framework
     */
    static async install() {
        const projectPath = Editor.Project.path;
        const command = 'npm install @esengine/ecs-framework';
        console.log(`Installing ECS Framework to project: ${projectPath}`);
        return new Promise((resolve, reject) => {
            (0, child_process_1.exec)(command, { cwd: projectPath }, (error, stdout, stderr) => {
                console.log('Install stdout:', stdout);
                if (stderr)
                    console.log('Install stderr:', stderr);
                if (error) {
                    console.error('Installation failed:', error);
                    reject(error);
                }
                else {
                    console.log('Installation completed successfully');
                    // 验证安装是否成功
                    const nodeModulesPath = path.join(projectPath, 'node_modules', '@esengine', 'ecs-framework');
                    const installSuccess = fs.existsSync(nodeModulesPath);
                    if (installSuccess) {
                        console.log('ECS Framework installed successfully');
                        resolve();
                    }
                    else {
                        console.warn('ECS Framework directory not found after install');
                        reject(new Error('安装验证失败'));
                    }
                }
            });
        });
    }
    /**
     * 更新ECS Framework
     */
    static async update(targetVersion) {
        const projectPath = Editor.Project.path;
        const version = targetVersion ? `@${targetVersion}` : '@latest';
        const command = `npm install @esengine/ecs-framework${version}`;
        console.log(`Updating ECS Framework to ${version} in project: ${projectPath}`);
        return new Promise((resolve, reject) => {
            (0, child_process_1.exec)(command, { cwd: projectPath }, (error, stdout, stderr) => {
                console.log('Update stdout:', stdout);
                if (stderr)
                    console.log('Update stderr:', stderr);
                if (error) {
                    console.error('Update failed:', error);
                    reject(error);
                }
                else {
                    console.log('Update completed successfully');
                    // 验证更新是否成功
                    const nodeModulesPath = path.join(projectPath, 'node_modules', '@esengine', 'ecs-framework');
                    const updateSuccess = fs.existsSync(nodeModulesPath);
                    if (updateSuccess) {
                        console.log(`ECS Framework updated successfully to ${version}`);
                        resolve();
                    }
                    else {
                        console.warn('ECS Framework directory not found after update');
                        reject(new Error('更新验证失败'));
                    }
                }
            });
        });
    }
    /**
     * 卸载ECS Framework
     */
    static async uninstall() {
        const projectPath = Editor.Project.path;
        const command = 'npm uninstall @esengine/ecs-framework';
        console.log(`Uninstalling ECS Framework from project: ${projectPath}`);
        return new Promise((resolve, reject) => {
            (0, child_process_1.exec)(command, { cwd: projectPath }, (error, stdout, stderr) => {
                console.log('Uninstall stdout:', stdout);
                if (stderr)
                    console.log('Uninstall stderr:', stderr);
                if (error) {
                    console.error('Uninstall failed:', error);
                    reject(error);
                }
                else {
                    console.log('Uninstall completed successfully');
                    // 检查是否真的卸载了
                    const nodeModulesPath = path.join(projectPath, 'node_modules', '@esengine', 'ecs-framework');
                    const stillExists = fs.existsSync(nodeModulesPath);
                    if (stillExists) {
                        console.warn('ECS Framework directory still exists after uninstall');
                        reject(new Error('卸载验证失败'));
                    }
                    else {
                        console.log('ECS Framework uninstalled successfully');
                        resolve();
                    }
                }
            });
        });
    }
    /**
     * 打开文档
     */
    static openDocumentation() {
        const url = 'https://github.com/esengine/ecs-framework/blob/master/README.md';
        try {
            // 使用Electron的shell模块打开外部链接
            const { shell } = require('electron');
            shell.openExternal(url);
            console.log('Documentation link opened successfully');
        }
        catch (error) {
            console.error('Failed to open documentation:', error);
            Editor.Dialog.info('打开文档', {
                detail: `请手动访问以下链接查看文档:\n\n${url}`,
            });
        }
    }
    /**
     * 创建ECS模板
     */
    static createTemplate() {
        const projectPath = Editor.Project.path;
        console.log(`Creating ECS template in project: ${projectPath}`);
        try {
            const templateGenerator = new TemplateGenerator_1.TemplateGenerator(projectPath);
            // 检查是否已存在模板
            if (templateGenerator.checkTemplateExists()) {
                const existingFiles = templateGenerator.getExistingFiles();
                const fileList = existingFiles.length > 0 ? existingFiles.join('\n• ') : '未检测到具体文件';
                Editor.Dialog.warn('模板已存在', {
                    detail: `检测到已存在ECS模板，包含以下文件：\n\n• ${fileList}\n\n是否要覆盖现有模板？`,
                    buttons: ['覆盖', '取消'],
                }).then((result) => {
                    if (result.response === 0) {
                        // 用户选择覆盖
                        console.log('User chose to overwrite existing template');
                        templateGenerator.removeExistingTemplate();
                        templateGenerator.createTemplate();
                        this.showTemplateCreatedDialog();
                    }
                    else {
                        console.log('User cancelled template creation');
                    }
                });
                return;
            }
            // 创建新模板
            templateGenerator.createTemplate();
            console.log('ECS template created successfully');
            this.showTemplateCreatedDialog();
        }
        catch (error) {
            console.error('Failed to create ECS template:', error);
            const errorMessage = error instanceof Error ? error.message : String(error);
            Editor.Dialog.error('模板创建失败', {
                detail: `创建ECS模板时发生错误：\n\n${errorMessage}\n\n请检查项目权限和目录结构。`,
            });
        }
    }
    /**
     * 显示模板创建成功的对话框
     */
    static showTemplateCreatedDialog() {
        Editor.Dialog.info('模板创建成功', {
            detail: '✅ ECS项目模板已创建完成！\n\n已为您的Cocos Creator项目生成了完整的ECS架构模板，包括：\n\n' +
                '• 位置、速度、Cocos节点组件\n' +
                '• 移动系统和节点同步系统\n' +
                '• 实体工厂和场景管理器\n' +
                '• ECS管理器组件(可直接添加到节点)\n' +
                '• 完整的使用文档\n\n' +
                '请刷新资源管理器查看新创建的文件。',
        });
    }
    /**
     * 打开GitHub仓库
     */
    static openGitHub() {
        const url = 'https://github.com/esengine/ecs-framework';
        try {
            const { shell } = require('electron');
            shell.openExternal(url);
            console.log('GitHub repository opened successfully');
        }
        catch (error) {
            console.error('Failed to open GitHub repository:', error);
            Editor.Dialog.info('打开GitHub', {
                detail: `请手动访问以下链接：\n\n${url}`,
            });
        }
    }
    /**
     * 打开QQ群
     */
    static openQQGroup() {
        const url = 'https://qm.qq.com/cgi-bin/qm/qr?k=your-qq-group-key';
        try {
            const { shell } = require('electron');
            shell.openExternal(url);
            console.log('QQ group opened successfully');
        }
        catch (error) {
            console.error('Failed to open QQ group:', error);
            Editor.Dialog.info('QQ群', {
                detail: '请手动搜索QQ群号或访问相关链接加入讨论群。',
            });
        }
    }
    // ================ 资源菜单相关功能 ================
    /**
     * 创建ECS组件
     */
    static async createComponent(assetInfo) {
        try {
            const result = await Editor.Dialog.save({
                title: '创建ECS组件',
                filters: [
                    { name: 'TypeScript文件', extensions: ['ts'] }
                ]
            });
            if (result.canceled || !result.filePath) {
                return;
            }
            const componentName = path.basename(result.filePath, '.ts');
            const componentContent = this.generateComponentTemplate(componentName);
            await fs.promises.writeFile(result.filePath, componentContent);
            // 刷新资源管理器
            await Editor.Message.request('asset-db', 'refresh-asset', 'db://assets');
            Editor.Dialog.info('创建成功', {
                detail: `ECS组件 "${componentName}" 已创建完成！\n\n文件路径：${result.filePath}`
            });
        }
        catch (error) {
            console.error('创建ECS组件失败:', error);
            Editor.Dialog.error('创建失败', {
                detail: `创建ECS组件时发生错误：${error instanceof Error ? error.message : String(error)}`
            });
        }
    }
    /**
     * 创建ECS系统
     */
    static async createSystem(assetInfo) {
        try {
            const result = await Editor.Dialog.save({
                title: '创建ECS系统',
                filters: [
                    { name: 'TypeScript文件', extensions: ['ts'] }
                ]
            });
            if (result.canceled || !result.filePath) {
                return;
            }
            const systemName = path.basename(result.filePath, '.ts');
            const systemContent = this.generateSystemTemplate(systemName);
            await fs.promises.writeFile(result.filePath, systemContent);
            // 刷新资源管理器
            await Editor.Message.request('asset-db', 'refresh-asset', 'db://assets');
            Editor.Dialog.info('创建成功', {
                detail: `ECS系统 "${systemName}" 已创建完成！\n\n文件路径：${result.filePath}`
            });
        }
        catch (error) {
            console.error('创建ECS系统失败:', error);
            Editor.Dialog.error('创建失败', {
                detail: `创建ECS系统时发生错误：${error instanceof Error ? error.message : String(error)}`
            });
        }
    }
    /**
     * 重新生成模板代码
     */
    static async regenerateTemplate(assetInfo) {
        try {
            Editor.Dialog.info('重新生成模板', {
                detail: '此功能将重新生成选中文件的模板代码。请注意这可能会覆盖您的自定义修改。',
                buttons: ['继续', '取消']
            }).then((result) => {
                if (result.response === 0) {
                    // 用户确认继续
                    console.log('重新生成模板代码:', assetInfo.file);
                    Editor.Dialog.info('功能开发中', {
                        detail: '模板重新生成功能正在开发中，敬请期待！'
                    });
                }
            });
        }
        catch (error) {
            console.error('重新生成模板失败:', error);
            Editor.Dialog.error('操作失败', {
                detail: `重新生成模板时发生错误：${error instanceof Error ? error.message : String(error)}`
            });
        }
    }
    /**
     * 添加到ECS管理器
     */
    static async addToManager(assetInfo) {
        try {
            const fileName = path.basename(assetInfo.file, '.ts');
            Editor.Dialog.info('添加到ECS管理器', {
                detail: `将 "${fileName}" 添加到ECS管理器中，这将自动在ECSManager中注册该组件或系统。`,
                buttons: ['确认添加', '取消']
            }).then((result) => {
                if (result.response === 0) {
                    // 用户确认添加
                    console.log('添加到ECS管理器:', assetInfo.file);
                    Editor.Dialog.info('功能开发中', {
                        detail: '自动添加到ECS管理器功能正在开发中，敬请期待！'
                    });
                }
            });
        }
        catch (error) {
            console.error('添加到ECS管理器失败:', error);
            Editor.Dialog.error('操作失败', {
                detail: `添加到ECS管理器时发生错误：${error instanceof Error ? error.message : String(error)}`
            });
        }
    }
    // ================ 模板生成相关 ================
    /**
     * 生成组件模板
     */
    static generateComponentTemplate(componentName) {
        return `import { Component } from '@esengine/ecs-framework';

/**
 * ${componentName}组件
 */
export class ${componentName} extends Component {
    // 在这里添加组件的属性
    
    constructor() {
        super();
    }
    
    public toString(): string {
        return \`${componentName}\`;
    }
}
`;
    }
    /**
     * 生成系统模板
     */
    static generateSystemTemplate(systemName) {
        return `import { EntitySystem, Entity, Matcher } from '@esengine/ecs-framework';

/**
 * ${systemName}系统
 */
export class ${systemName} extends EntitySystem {
    
    constructor() {
        // 在这里定义系统关注的组件匹配器
        super(Matcher.empty());
    }
    
    protected processEntity(entity: Entity, deltaTime: number): void {
        // 在这里处理每个实体的逻辑
    }
    
    protected begin(): void {
        // 系统开始处理前的逻辑
    }
    
    protected end(): void {
        // 系统处理完成后的逻辑
    }
}
`;
    }
}
exports.EcsFrameworkHandler = EcsFrameworkHandler;
//# sourceMappingURL=data:application/json;base64,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