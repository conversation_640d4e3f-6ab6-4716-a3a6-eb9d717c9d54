"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const fs_extra_1 = require("fs-extra");
const path_1 = require("path");
const vue_1 = require("vue");
const ws_1 = require("ws");
const panelDataMap = new WeakMap();
/**
 * ECS调试服务器
 * 作为服务端，接收多个游戏实例的连接
 */
class ECSDebugServer {
    constructor(port = 8080) {
        this.port = 8080;
        this.gameInstances = new Map();
        this.isRunning = false;
        this.port = port;
    }
    async start() {
        if (this.isRunning)
            return true;
        try {
            this.wss = new ws_1.WebSocketServer({ port: this.port });
            this.wss.on('connection', (ws, req) => {
                const instanceId = this.generateInstanceId();
                const instance = {
                    id: instanceId,
                    name: `游戏实例-${instanceId.substring(0, 8)}`,
                    connectTime: Date.now(),
                    lastUpdateTime: Date.now(),
                    isActive: true,
                    debugData: null,
                    ws: ws
                };
                this.gameInstances.set(instanceId, instance);
                console.log(`[ECS Debug Server] New instance connected: ${instance.name}`);
                ws.on('message', (data) => {
                    try {
                        const message = JSON.parse(data.toString());
                        this.handleMessage(instanceId, message);
                    }
                    catch (error) {
                        console.error('[ECS Debug Server] Failed to parse message:', error);
                    }
                });
                ws.on('close', () => {
                    const instance = this.gameInstances.get(instanceId);
                    if (instance) {
                        instance.isActive = false;
                        console.log(`[ECS Debug Server] Instance disconnected: ${instance.name}`);
                    }
                });
                ws.on('error', (error) => {
                    console.error(`[ECS Debug Server] WebSocket error for ${instanceId}:`, error);
                });
                // 发送连接确认
                this.sendToInstance(instanceId, {
                    type: 'connection_confirmed',
                    instanceId: instanceId,
                    serverTime: Date.now()
                });
            });
            this.isRunning = true;
            console.log(`[ECS Debug Server] Started on port ${this.port}`);
            return true;
        }
        catch (error) {
            console.error('[ECS Debug Server] Failed to start:', error);
            return false;
        }
    }
    stop() {
        if (this.wss) {
            this.wss.close();
            this.wss = undefined;
        }
        this.gameInstances.clear();
        this.isRunning = false;
        console.log('[ECS Debug Server] Stopped');
    }
    generateInstanceId() {
        return Math.random().toString(36).substring(2) + Date.now().toString(36);
    }
    handleMessage(instanceId, message) {
        const instance = this.gameInstances.get(instanceId);
        if (!instance)
            return;
        switch (message.type) {
            case 'debug_data':
                instance.debugData = message.data;
                instance.lastUpdateTime = Date.now();
                break;
            case 'instance_info':
                if (message.name) {
                    instance.name = message.name;
                }
                break;
            case 'ping':
                this.sendToInstance(instanceId, { type: 'pong', timestamp: Date.now() });
                break;
        }
    }
    sendToInstance(instanceId, message) {
        const instance = this.gameInstances.get(instanceId);
        if (instance && instance.ws && instance.ws.readyState === 1) {
            instance.ws.send(JSON.stringify(message));
        }
    }
    get running() {
        return this.isRunning;
    }
    get instances() {
        return Array.from(this.gameInstances.values());
    }
    getInstance(instanceId) {
        return this.gameInstances.get(instanceId);
    }
    getInstanceDebugData(instanceId) {
        const instance = this.gameInstances.get(instanceId);
        if (!instance || !instance.debugData) {
            return null;
        }
        return this.transformToDetailedDebugInfo(instance, instance.debugData);
    }
    transformToDetailedDebugInfo(instance, rawData) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, _16, _17, _18, _19, _20, _21, _22, _23, _24, _25, _26, _27, _28;
        const uptime = (Date.now() - instance.connectTime) / 1000;
        // 计算系统性能数据，包括ECS占比
        const systemBreakdown = ((_a = rawData.performance) === null || _a === void 0 ? void 0 : _a.systemBreakdown) || [];
        const systemPerformance = ((_b = rawData.performance) === null || _b === void 0 ? void 0 : _b.systemPerformance) || [];
        // 创建系统名称到占比的映射
        const systemPercentageMap = new Map();
        systemBreakdown.forEach((sys) => {
            systemPercentageMap.set(sys.systemName, sys.percentage || 0);
        });
        return {
            instanceId: instance.id,
            instanceName: instance.name,
            isRunning: rawData.isRunning || false,
            frameworkLoaded: rawData.frameworkLoaded || false,
            currentScene: rawData.currentScene || '未知',
            uptime: uptime,
            performance: {
                frameTime: ((_c = rawData.performance) === null || _c === void 0 ? void 0 : _c.frameTime) || 0,
                fps: ((_d = rawData.performance) === null || _d === void 0 ? void 0 : _d.fps) || 0,
                averageFrameTime: ((_e = rawData.performance) === null || _e === void 0 ? void 0 : _e.averageFrameTime) || ((_f = rawData.performance) === null || _f === void 0 ? void 0 : _f.frameTime) || 0,
                minFrameTime: ((_g = rawData.performance) === null || _g === void 0 ? void 0 : _g.minFrameTime) || ((_h = rawData.performance) === null || _h === void 0 ? void 0 : _h.frameTime) || 0,
                maxFrameTime: ((_j = rawData.performance) === null || _j === void 0 ? void 0 : _j.maxFrameTime) || ((_k = rawData.performance) === null || _k === void 0 ? void 0 : _k.frameTime) || 0,
                frameTimeHistory: ((_l = rawData.performance) === null || _l === void 0 ? void 0 : _l.frameTimeHistory) || [],
                engineFrameTime: ((_m = rawData.performance) === null || _m === void 0 ? void 0 : _m.engineFrameTime) || 0,
                ecsPercentage: ((_o = rawData.performance) === null || _o === void 0 ? void 0 : _o.ecsPercentage) || 0
            },
            memory: {
                totalMemory: ((_q = (_p = rawData.performance) === null || _p === void 0 ? void 0 : _p.memoryDetails) === null || _q === void 0 ? void 0 : _q.totalMemory) || ((_r = rawData.memory) === null || _r === void 0 ? void 0 : _r.totalMemory) || 512 * 1024 * 1024,
                usedMemory: ((_t = (_s = rawData.performance) === null || _s === void 0 ? void 0 : _s.memoryDetails) === null || _t === void 0 ? void 0 : _t.usedMemory) || (((_u = rawData.performance) === null || _u === void 0 ? void 0 : _u.memoryUsage) ? rawData.performance.memoryUsage * 1024 * 1024 : 0),
                freeMemory: ((_w = (_v = rawData.performance) === null || _v === void 0 ? void 0 : _v.memoryDetails) === null || _w === void 0 ? void 0 : _w.freeMemory) || 0,
                entityMemory: ((_y = (_x = rawData.performance) === null || _x === void 0 ? void 0 : _x.memoryDetails) === null || _y === void 0 ? void 0 : _y.entities) || ((_z = rawData.memory) === null || _z === void 0 ? void 0 : _z.entityMemory) || 0,
                componentMemory: ((_1 = (_0 = rawData.performance) === null || _0 === void 0 ? void 0 : _0.memoryDetails) === null || _1 === void 0 ? void 0 : _1.components) || ((_2 = rawData.memory) === null || _2 === void 0 ? void 0 : _2.componentMemory) || 0,
                systemMemory: ((_4 = (_3 = rawData.performance) === null || _3 === void 0 ? void 0 : _3.memoryDetails) === null || _4 === void 0 ? void 0 : _4.systems) || ((_5 = rawData.memory) === null || _5 === void 0 ? void 0 : _5.systemMemory) || 0,
                pooledMemory: ((_7 = (_6 = rawData.performance) === null || _6 === void 0 ? void 0 : _6.memoryDetails) === null || _7 === void 0 ? void 0 : _7.pooled) || ((_8 = rawData.memory) === null || _8 === void 0 ? void 0 : _8.pooledMemory) || 0,
                gcCollections: ((_10 = (_9 = rawData.performance) === null || _9 === void 0 ? void 0 : _9.memoryDetails) === null || _10 === void 0 ? void 0 : _10.gcCollections) || ((_11 = rawData.memory) === null || _11 === void 0 ? void 0 : _11.gcCollections) || 0
            },
            entities: {
                total: ((_12 = rawData.entities) === null || _12 === void 0 ? void 0 : _12.totalEntities) || 0,
                active: ((_13 = rawData.entities) === null || _13 === void 0 ? void 0 : _13.activeEntities) || 0,
                inactive: (((_14 = rawData.entities) === null || _14 === void 0 ? void 0 : _14.totalEntities) || 0) - (((_15 = rawData.entities) === null || _15 === void 0 ? void 0 : _15.activeEntities) || 0),
                pendingAdd: ((_16 = rawData.entities) === null || _16 === void 0 ? void 0 : _16.pendingAdd) || 0,
                pendingRemove: ((_17 = rawData.entities) === null || _17 === void 0 ? void 0 : _17.pendingRemove) || 0,
                entitiesPerArchetype: ((_18 = rawData.entities) === null || _18 === void 0 ? void 0 : _18.entitiesPerArchetype) || [],
                topEntitiesByComponents: ((_19 = rawData.entities) === null || _19 === void 0 ? void 0 : _19.topEntitiesByComponents) || []
            },
            components: {
                totalTypes: ((_20 = rawData.components) === null || _20 === void 0 ? void 0 : _20.componentTypes) || 0,
                totalInstances: ((_21 = rawData.components) === null || _21 === void 0 ? void 0 : _21.componentInstances) || 0,
                componentStats: (((_22 = rawData.components) === null || _22 === void 0 ? void 0 : _22.componentStats) || []).map((comp) => ({
                    typeName: comp.typeName,
                    instanceCount: comp.instanceCount || 0,
                    memoryPerInstance: comp.memoryPerInstance || 0,
                    totalMemory: comp.totalMemory || (comp.instanceCount || 0) * (comp.memoryPerInstance || 0),
                    poolSize: comp.poolSize || 0,
                    poolUtilization: comp.poolSize > 0 ? (comp.instanceCount / comp.poolSize * 100) : 0
                }))
            },
            systems: {
                total: ((_23 = rawData.systems) === null || _23 === void 0 ? void 0 : _23.totalSystems) || 0,
                systemStats: (((_24 = rawData.systems) === null || _24 === void 0 ? void 0 : _24.systemsInfo) || []).map((sys) => {
                    const systemName = sys.name;
                    const percentage = systemPercentageMap.get(systemName) || 0;
                    return {
                        name: systemName,
                        type: sys.type || 'Unknown',
                        entityCount: sys.entityCount || 0,
                        averageExecutionTime: sys.executionTime || 0,
                        minExecutionTime: sys.minExecutionTime || sys.executionTime || 0,
                        maxExecutionTime: sys.maxExecutionTime || sys.executionTime || 0,
                        executionTimeHistory: sys.executionTimeHistory || [],
                        memoryUsage: sys.memoryUsage || 0,
                        updateOrder: sys.updateOrder || 0,
                        enabled: sys.enabled !== false,
                        percentage: percentage
                    };
                })
            },
            scenes: {
                currentScene: rawData.currentScene || '未知',
                sceneMemory: ((_25 = rawData.scenes) === null || _25 === void 0 ? void 0 : _25.sceneMemory) || 0,
                sceneEntityCount: ((_26 = rawData.entities) === null || _26 === void 0 ? void 0 : _26.totalEntities) || 0,
                sceneSystemCount: ((_27 = rawData.systems) === null || _27 === void 0 ? void 0 : _27.totalSystems) || 0,
                sceneUptime: ((_28 = rawData.scenes) === null || _28 === void 0 ? void 0 : _28.sceneUptime) || uptime
            }
        };
    }
}
/**
 * 默认调试信息
 */
const defaultDebugInfo = {
    instanceId: '',
    instanceName: '未选择实例',
    isRunning: false,
    frameworkLoaded: false,
    currentScene: '未知',
    uptime: 0,
    performance: {
        frameTime: 0,
        fps: 0,
        averageFrameTime: 0,
        minFrameTime: 0,
        maxFrameTime: 0,
        frameTimeHistory: [],
        engineFrameTime: 0,
        ecsPercentage: 0
    },
    memory: {
        totalMemory: 0,
        usedMemory: 0,
        freeMemory: 0,
        entityMemory: 0,
        componentMemory: 0,
        systemMemory: 0,
        pooledMemory: 0,
        gcCollections: 0
    },
    entities: {
        total: 0,
        active: 0,
        inactive: 0,
        pendingAdd: 0,
        pendingRemove: 0,
        entitiesPerArchetype: [],
        topEntitiesByComponents: []
    },
    components: {
        totalTypes: 0,
        totalInstances: 0,
        componentStats: []
    },
    systems: {
        total: 0,
        systemStats: []
    },
    scenes: {
        currentScene: '未知',
        sceneMemory: 0,
        sceneEntityCount: 0,
        sceneSystemCount: 0,
        sceneUptime: 0
    }
};
// 全局调试服务器实例
let globalDebugServer = null;
/**
 * 启动调试服务器
 */
async function ensureDebugServer() {
    if (!globalDebugServer) {
        globalDebugServer = new ECSDebugServer(8080);
    }
    if (!globalDebugServer.running) {
        await globalDebugServer.start();
    }
    return globalDebugServer;
}
module.exports = Editor.Panel.define({
    listeners: {
        show() { },
        hide() { },
    },
    template: `<div id="app"></div>`,
    style: (0, fs_extra_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/style/debug/index.css'), 'utf-8'),
    $: {
        app: '#app',
    },
    ready() {
        if (this.$.app) {
            const app = (0, vue_1.createApp)((0, vue_1.defineComponent)({
                setup() {
                    const debugInfo = (0, vue_1.reactive)(Object.assign({}, defaultDebugInfo));
                    const gameInstances = (0, vue_1.ref)([]);
                    const selectedInstanceId = (0, vue_1.ref)('');
                    const isAutoRefresh = (0, vue_1.ref)(true);
                    const refreshInterval = (0, vue_1.ref)(100);
                    const lastUpdateTime = (0, vue_1.ref)('');
                    const showComponentPoolHelp = (0, vue_1.ref)(false);
                    let intervalId = null;
                    let debugServer = null;
                    // 初始化调试服务器
                    const initializeServer = async () => {
                        try {
                            debugServer = await ensureDebugServer();
                        }
                        catch (error) {
                            console.error('Failed to start debug server:', error);
                        }
                    };
                    // 更新游戏实例列表和调试信息
                    const updateDebugInfo = async () => {
                        if (!debugServer)
                            return;
                        try {
                            // 更新实例列表
                            gameInstances.value = debugServer.instances;
                            // 如果有选中的实例，更新其调试信息
                            if (selectedInstanceId.value) {
                                const detailedInfo = debugServer.getInstanceDebugData(selectedInstanceId.value);
                                if (detailedInfo) {
                                    Object.assign(debugInfo, detailedInfo);
                                }
                                else {
                                    // 实例已断开，重置选择
                                    selectedInstanceId.value = '';
                                    Object.assign(debugInfo, defaultDebugInfo);
                                }
                            }
                            lastUpdateTime.value = new Date().toLocaleTimeString();
                        }
                        catch (error) {
                            console.error('Failed to update debug info:', error);
                        }
                    };
                    // 开始自动刷新
                    const startAutoRefresh = () => {
                        if (intervalId)
                            clearInterval(intervalId);
                        if (isAutoRefresh.value) {
                            intervalId = setInterval(updateDebugInfo, refreshInterval.value);
                        }
                    };
                    // 停止自动刷新
                    const stopAutoRefresh = () => {
                        if (intervalId) {
                            clearInterval(intervalId);
                            intervalId = null;
                        }
                    };
                    // 手动刷新
                    const manualRefresh = () => {
                        updateDebugInfo();
                    };
                    // 切换自动刷新
                    const toggleAutoRefresh = () => {
                        if (isAutoRefresh.value) {
                            startAutoRefresh();
                        }
                        else {
                            stopAutoRefresh();
                        }
                    };
                    // 更改刷新间隔
                    const changeRefreshInterval = () => {
                        if (isAutoRefresh.value) {
                            startAutoRefresh();
                        }
                    };
                    // 实例选择改变
                    const onInstanceChanged = () => {
                        if (selectedInstanceId.value) {
                            updateDebugInfo();
                        }
                        else {
                            Object.assign(debugInfo, defaultDebugInfo);
                        }
                    };
                    // 格式化运行时间
                    const formatUptime = (seconds) => {
                        const hours = Math.floor(seconds / 3600);
                        const minutes = Math.floor((seconds % 3600) / 60);
                        const secs = Math.floor(seconds % 60);
                        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                    };
                    // 格式化内存大小
                    const formatMemory = (bytes) => {
                        if (bytes < 1024)
                            return bytes + ' B';
                        if (bytes < 1024 * 1024)
                            return (bytes / 1024).toFixed(1) + ' KB';
                        if (bytes < 1024 * 1024 * 1024)
                            return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
                        return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
                    };
                    // 获取FPS颜色
                    const getFpsColor = (fps) => {
                        if (fps >= 55)
                            return 'good';
                        if (fps >= 30)
                            return 'warning';
                        return 'critical';
                    };
                    // 获取内存颜色
                    const getMemoryColor = (percentage) => {
                        if (percentage < 70)
                            return 'good';
                        if (percentage < 85)
                            return 'warning';
                        return 'critical';
                    };
                    // 获取ECS时间占比颜色
                    const getECSTimeColor = (percentage) => {
                        if (!percentage)
                            return 'good';
                        if (percentage <= 10)
                            return 'good'; // ECS占用<=10%为绿色
                        if (percentage <= 30)
                            return 'warning'; // ECS占用<=30%为黄色
                        return 'critical'; // ECS占用>30%为红色
                    };
                    // 获取执行时间颜色
                    const getExecutionTimeColor = (time) => {
                        if (time < 1)
                            return 'good'; // <1ms为绿色
                        if (time < 5)
                            return 'warning'; // <5ms为黄色
                        return 'critical';
                    };
                    // 打开文档链接  
                    const openDocumentation = (section) => {
                        const urls = {
                            'component-pool': 'https://github.com/esengine/ecs-framework/tree/master/docs/component-design-guide.md#1-对象池优化',
                            'performance-optimization': 'https://github.com/esengine/ecs-framework/tree/master/docs/performance-optimization.md'
                        };
                        const url = urls[section];
                        if (!url)
                            return;
                        try {
                            // 在Cocos Creator扩展环境中，直接使用Electron的shell模块
                            const { shell } = require('electron');
                            shell.openExternal(url);
                        }
                        catch (error) {
                            console.error('无法打开链接:', error);
                            // 如果失败，复制到剪贴板
                            copyUrlToClipboard(url);
                        }
                    };
                    // 复制链接到剪贴板的辅助函数
                    const copyUrlToClipboard = (url) => {
                        try {
                            // 尝试使用现代的剪贴板API
                            if (navigator.clipboard && navigator.clipboard.writeText) {
                                navigator.clipboard.writeText(url).then(() => {
                                    console.log(`文档链接已复制到剪贴板: ${url}`);
                                    // 如果可能的话，显示用户友好的提示
                                    if (typeof alert !== 'undefined') {
                                        alert(`文档链接已复制到剪贴板，请在浏览器中粘贴访问:\n${url}`);
                                    }
                                }).catch(() => {
                                    fallbackCopyText(url);
                                });
                            }
                            else {
                                fallbackCopyText(url);
                            }
                        }
                        catch (error) {
                            fallbackCopyText(url);
                        }
                    };
                    // 备用的复制文本方法
                    const fallbackCopyText = (text) => {
                        try {
                            // 创建临时的文本区域
                            const textArea = document.createElement('textarea');
                            textArea.value = text;
                            textArea.style.position = 'fixed';
                            textArea.style.left = '-999999px';
                            textArea.style.top = '-999999px';
                            document.body.appendChild(textArea);
                            textArea.focus();
                            textArea.select();
                            const successful = document.execCommand('copy');
                            document.body.removeChild(textArea);
                            if (successful) {
                                console.log(`文档链接已复制到剪贴板: ${text}`);
                                if (typeof alert !== 'undefined') {
                                    alert(`文档链接已复制到剪贴板，请在浏览器中粘贴访问:\n${text}`);
                                }
                            }
                            else {
                                console.log(`请手动复制文档链接: ${text}`);
                                if (typeof alert !== 'undefined') {
                                    alert(`请手动复制文档链接:\n${text}`);
                                }
                            }
                        }
                        catch (error) {
                            console.log(`请手动访问文档: ${text}`);
                            if (typeof alert !== 'undefined') {
                                alert(`请手动访问文档:\n${text}`);
                            }
                        }
                    };
                    // 组件挂载时初始化
                    (0, vue_1.onMounted)(async () => {
                        await initializeServer();
                        updateDebugInfo();
                        startAutoRefresh();
                    });
                    // 组件卸载时清理
                    (0, vue_1.onUnmounted)(() => {
                        stopAutoRefresh();
                    });
                    return {
                        debugInfo,
                        gameInstances,
                        selectedInstanceId,
                        isAutoRefresh,
                        refreshInterval,
                        lastUpdateTime,
                        showComponentPoolHelp,
                        manualRefresh,
                        toggleAutoRefresh,
                        changeRefreshInterval,
                        onInstanceChanged,
                        formatUptime,
                        formatMemory,
                        getFpsColor,
                        getMemoryColor,
                        getECSTimeColor,
                        getExecutionTimeColor,
                        openDocumentation
                    };
                },
                template: (0, fs_extra_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/template/debug/index.html'), 'utf-8'),
            }));
            app.config.compilerOptions.isCustomElement = (tag) => tag.startsWith('ui-');
            app.mount(this.$.app);
            panelDataMap.set(this, app);
        }
    },
    beforeClose() { },
    close() {
        const app = panelDataMap.get(this);
        if (app) {
            app.unmount();
            panelDataMap.delete(this);
        }
        // 关闭调试服务器
        if (globalDebugServer) {
            globalDebugServer.stop();
            globalDebugServer = null;
        }
    },
});
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi9zb3VyY2UvcGFuZWxzL2RlYnVnL2luZGV4LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7O0FBQUEsdUNBQXdDO0FBQ3hDLCtCQUE0QjtBQUM1Qiw2QkFBNkY7QUFDN0YsMkJBQWdEO0FBR2hELE1BQU0sWUFBWSxHQUFHLElBQUksT0FBTyxFQUFZLENBQUM7QUFpSDdDOzs7R0FHRztBQUNILE1BQU0sY0FBYztJQU1oQixZQUFZLE9BQWUsSUFBSTtRQUp2QixTQUFJLEdBQVcsSUFBSSxDQUFDO1FBQ3BCLGtCQUFhLEdBQUcsSUFBSSxHQUFHLEVBQXdCLENBQUM7UUFDaEQsY0FBUyxHQUFZLEtBQUssQ0FBQztRQUcvQixJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQztJQUNyQixDQUFDO0lBRUQsS0FBSyxDQUFDLEtBQUs7UUFDUCxJQUFJLElBQUksQ0FBQyxTQUFTO1lBQUUsT0FBTyxJQUFJLENBQUM7UUFFaEMsSUFBSSxDQUFDO1lBQ0QsSUFBSSxDQUFDLEdBQUcsR0FBRyxJQUFJLG9CQUFlLENBQUMsRUFBRSxJQUFJLEVBQUUsSUFBSSxDQUFDLElBQUksRUFBRSxDQUFDLENBQUM7WUFFcEQsSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsWUFBWSxFQUFFLENBQUMsRUFBYSxFQUFFLEdBQW9CLEVBQUUsRUFBRTtnQkFDOUQsTUFBTSxVQUFVLEdBQUcsSUFBSSxDQUFDLGtCQUFrQixFQUFFLENBQUM7Z0JBQzdDLE1BQU0sUUFBUSxHQUFpQjtvQkFDM0IsRUFBRSxFQUFFLFVBQVU7b0JBQ2QsSUFBSSxFQUFFLFFBQVEsVUFBVSxDQUFDLFNBQVMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUU7b0JBQzFDLFdBQVcsRUFBRSxJQUFJLENBQUMsR0FBRyxFQUFFO29CQUN2QixjQUFjLEVBQUUsSUFBSSxDQUFDLEdBQUcsRUFBRTtvQkFDMUIsUUFBUSxFQUFFLElBQUk7b0JBQ2QsU0FBUyxFQUFFLElBQUk7b0JBQ2YsRUFBRSxFQUFFLEVBQUU7aUJBQ1QsQ0FBQztnQkFFRixJQUFJLENBQUMsYUFBYSxDQUFDLEdBQUcsQ0FBQyxVQUFVLEVBQUUsUUFBUSxDQUFDLENBQUM7Z0JBQzdDLE9BQU8sQ0FBQyxHQUFHLENBQUMsOENBQThDLFFBQVEsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDO2dCQUUzRSxFQUFFLENBQUMsRUFBRSxDQUFDLFNBQVMsRUFBRSxDQUFDLElBQVksRUFBRSxFQUFFO29CQUM5QixJQUFJLENBQUM7d0JBQ0QsTUFBTSxPQUFPLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQzt3QkFDNUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxVQUFVLEVBQUUsT0FBTyxDQUFDLENBQUM7b0JBQzVDLENBQUM7b0JBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQzt3QkFDYixPQUFPLENBQUMsS0FBSyxDQUFDLDZDQUE2QyxFQUFFLEtBQUssQ0FBQyxDQUFDO29CQUN4RSxDQUFDO2dCQUNMLENBQUMsQ0FBQyxDQUFDO2dCQUVILEVBQUUsQ0FBQyxFQUFFLENBQUMsT0FBTyxFQUFFLEdBQUcsRUFBRTtvQkFDaEIsTUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLGFBQWEsQ0FBQyxHQUFHLENBQUMsVUFBVSxDQUFDLENBQUM7b0JBQ3BELElBQUksUUFBUSxFQUFFLENBQUM7d0JBQ1gsUUFBUSxDQUFDLFFBQVEsR0FBRyxLQUFLLENBQUM7d0JBQzFCLE9BQU8sQ0FBQyxHQUFHLENBQUMsNkNBQTZDLFFBQVEsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDO29CQUM5RSxDQUFDO2dCQUNMLENBQUMsQ0FBQyxDQUFDO2dCQUVILEVBQUUsQ0FBQyxFQUFFLENBQUMsT0FBTyxFQUFFLENBQUMsS0FBWSxFQUFFLEVBQUU7b0JBQzVCLE9BQU8sQ0FBQyxLQUFLLENBQUMsMENBQTBDLFVBQVUsR0FBRyxFQUFFLEtBQUssQ0FBQyxDQUFDO2dCQUNsRixDQUFDLENBQUMsQ0FBQztnQkFFSCxTQUFTO2dCQUNULElBQUksQ0FBQyxjQUFjLENBQUMsVUFBVSxFQUFFO29CQUM1QixJQUFJLEVBQUUsc0JBQXNCO29CQUM1QixVQUFVLEVBQUUsVUFBVTtvQkFDdEIsVUFBVSxFQUFFLElBQUksQ0FBQyxHQUFHLEVBQUU7aUJBQ3pCLENBQUMsQ0FBQztZQUNQLENBQUMsQ0FBQyxDQUFDO1lBRUgsSUFBSSxDQUFDLFNBQVMsR0FBRyxJQUFJLENBQUM7WUFDdEIsT0FBTyxDQUFDLEdBQUcsQ0FBQyxzQ0FBc0MsSUFBSSxDQUFDLElBQUksRUFBRSxDQUFDLENBQUM7WUFDL0QsT0FBTyxJQUFJLENBQUM7UUFFaEIsQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDYixPQUFPLENBQUMsS0FBSyxDQUFDLHFDQUFxQyxFQUFFLEtBQUssQ0FBQyxDQUFDO1lBQzVELE9BQU8sS0FBSyxDQUFDO1FBQ2pCLENBQUM7SUFDTCxDQUFDO0lBRUQsSUFBSTtRQUNBLElBQUksSUFBSSxDQUFDLEdBQUcsRUFBRSxDQUFDO1lBQ1gsSUFBSSxDQUFDLEdBQUcsQ0FBQyxLQUFLLEVBQUUsQ0FBQztZQUNqQixJQUFJLENBQUMsR0FBRyxHQUFHLFNBQVMsQ0FBQztRQUN6QixDQUFDO1FBQ0QsSUFBSSxDQUFDLGFBQWEsQ0FBQyxLQUFLLEVBQUUsQ0FBQztRQUMzQixJQUFJLENBQUMsU0FBUyxHQUFHLEtBQUssQ0FBQztRQUN2QixPQUFPLENBQUMsR0FBRyxDQUFDLDRCQUE0QixDQUFDLENBQUM7SUFDOUMsQ0FBQztJQUVPLGtCQUFrQjtRQUN0QixPQUFPLElBQUksQ0FBQyxNQUFNLEVBQUUsQ0FBQyxRQUFRLENBQUMsRUFBRSxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxHQUFHLEVBQUUsQ0FBQyxRQUFRLENBQUMsRUFBRSxDQUFDLENBQUM7SUFDN0UsQ0FBQztJQUVPLGFBQWEsQ0FBQyxVQUFrQixFQUFFLE9BQVk7UUFDbEQsTUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLGFBQWEsQ0FBQyxHQUFHLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDcEQsSUFBSSxDQUFDLFFBQVE7WUFBRSxPQUFPO1FBRXRCLFFBQVEsT0FBTyxDQUFDLElBQUksRUFBRSxDQUFDO1lBQ25CLEtBQUssWUFBWTtnQkFDYixRQUFRLENBQUMsU0FBUyxHQUFHLE9BQU8sQ0FBQyxJQUFJLENBQUM7Z0JBQ2xDLFFBQVEsQ0FBQyxjQUFjLEdBQUcsSUFBSSxDQUFDLEdBQUcsRUFBRSxDQUFDO2dCQUNyQyxNQUFNO1lBRVYsS0FBSyxlQUFlO2dCQUNoQixJQUFJLE9BQU8sQ0FBQyxJQUFJLEVBQUUsQ0FBQztvQkFDZixRQUFRLENBQUMsSUFBSSxHQUFHLE9BQU8sQ0FBQyxJQUFJLENBQUM7Z0JBQ2pDLENBQUM7Z0JBQ0QsTUFBTTtZQUVWLEtBQUssTUFBTTtnQkFDUCxJQUFJLENBQUMsY0FBYyxDQUFDLFVBQVUsRUFBRSxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsU0FBUyxFQUFFLElBQUksQ0FBQyxHQUFHLEVBQUUsRUFBRSxDQUFDLENBQUM7Z0JBQ3pFLE1BQU07UUFDZCxDQUFDO0lBQ0wsQ0FBQztJQUVPLGNBQWMsQ0FBQyxVQUFrQixFQUFFLE9BQVk7UUFDbkQsTUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLGFBQWEsQ0FBQyxHQUFHLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDcEQsSUFBSSxRQUFRLElBQUksUUFBUSxDQUFDLEVBQUUsSUFBSSxRQUFRLENBQUMsRUFBRSxDQUFDLFVBQVUsS0FBSyxDQUFDLEVBQUUsQ0FBQztZQUMxRCxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUM7UUFDOUMsQ0FBQztJQUNMLENBQUM7SUFFRCxJQUFJLE9BQU87UUFDUCxPQUFPLElBQUksQ0FBQyxTQUFTLENBQUM7SUFDMUIsQ0FBQztJQUVELElBQUksU0FBUztRQUNULE9BQU8sS0FBSyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLE1BQU0sRUFBRSxDQUFDLENBQUM7SUFDbkQsQ0FBQztJQUVELFdBQVcsQ0FBQyxVQUFrQjtRQUMxQixPQUFPLElBQUksQ0FBQyxhQUFhLENBQUMsR0FBRyxDQUFDLFVBQVUsQ0FBQyxDQUFDO0lBQzlDLENBQUM7SUFFRCxvQkFBb0IsQ0FBQyxVQUFrQjtRQUNuQyxNQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsYUFBYSxDQUFDLEdBQUcsQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUNwRCxJQUFJLENBQUMsUUFBUSxJQUFJLENBQUMsUUFBUSxDQUFDLFNBQVMsRUFBRSxDQUFDO1lBQ25DLE9BQU8sSUFBSSxDQUFDO1FBQ2hCLENBQUM7UUFFRCxPQUFPLElBQUksQ0FBQyw0QkFBNEIsQ0FBQyxRQUFRLEVBQUUsUUFBUSxDQUFDLFNBQVMsQ0FBQyxDQUFDO0lBQzNFLENBQUM7SUFFTyw0QkFBNEIsQ0FBQyxRQUFzQixFQUFFLE9BQVk7O1FBQ3JFLE1BQU0sTUFBTSxHQUFHLENBQUMsSUFBSSxDQUFDLEdBQUcsRUFBRSxHQUFHLFFBQVEsQ0FBQyxXQUFXLENBQUMsR0FBRyxJQUFJLENBQUM7UUFFMUQsbUJBQW1CO1FBQ25CLE1BQU0sZUFBZSxHQUFHLENBQUEsTUFBQSxPQUFPLENBQUMsV0FBVywwQ0FBRSxlQUFlLEtBQUksRUFBRSxDQUFDO1FBQ25FLE1BQU0saUJBQWlCLEdBQUcsQ0FBQSxNQUFBLE9BQU8sQ0FBQyxXQUFXLDBDQUFFLGlCQUFpQixLQUFJLEVBQUUsQ0FBQztRQUV2RSxlQUFlO1FBQ2YsTUFBTSxtQkFBbUIsR0FBRyxJQUFJLEdBQUcsRUFBa0IsQ0FBQztRQUN0RCxlQUFlLENBQUMsT0FBTyxDQUFDLENBQUMsR0FBUSxFQUFFLEVBQUU7WUFDakMsbUJBQW1CLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxVQUFVLEVBQUUsR0FBRyxDQUFDLFVBQVUsSUFBSSxDQUFDLENBQUMsQ0FBQztRQUNqRSxDQUFDLENBQUMsQ0FBQztRQUVILE9BQU87WUFDSCxVQUFVLEVBQUUsUUFBUSxDQUFDLEVBQUU7WUFDdkIsWUFBWSxFQUFFLFFBQVEsQ0FBQyxJQUFJO1lBQzNCLFNBQVMsRUFBRSxPQUFPLENBQUMsU0FBUyxJQUFJLEtBQUs7WUFDckMsZUFBZSxFQUFFLE9BQU8sQ0FBQyxlQUFlLElBQUksS0FBSztZQUNqRCxZQUFZLEVBQUUsT0FBTyxDQUFDLFlBQVksSUFBSSxJQUFJO1lBQzFDLE1BQU0sRUFBRSxNQUFNO1lBRWQsV0FBVyxFQUFFO2dCQUNULFNBQVMsRUFBRSxDQUFBLE1BQUEsT0FBTyxDQUFDLFdBQVcsMENBQUUsU0FBUyxLQUFJLENBQUM7Z0JBQzlDLEdBQUcsRUFBRSxDQUFBLE1BQUEsT0FBTyxDQUFDLFdBQVcsMENBQUUsR0FBRyxLQUFJLENBQUM7Z0JBQ2xDLGdCQUFnQixFQUFFLENBQUEsTUFBQSxPQUFPLENBQUMsV0FBVywwQ0FBRSxnQkFBZ0IsTUFBSSxNQUFBLE9BQU8sQ0FBQyxXQUFXLDBDQUFFLFNBQVMsQ0FBQSxJQUFJLENBQUM7Z0JBQzlGLFlBQVksRUFBRSxDQUFBLE1BQUEsT0FBTyxDQUFDLFdBQVcsMENBQUUsWUFBWSxNQUFJLE1BQUEsT0FBTyxDQUFDLFdBQVcsMENBQUUsU0FBUyxDQUFBLElBQUksQ0FBQztnQkFDdEYsWUFBWSxFQUFFLENBQUEsTUFBQSxPQUFPLENBQUMsV0FBVywwQ0FBRSxZQUFZLE1BQUksTUFBQSxPQUFPLENBQUMsV0FBVywwQ0FBRSxTQUFTLENBQUEsSUFBSSxDQUFDO2dCQUN0RixnQkFBZ0IsRUFBRSxDQUFBLE1BQUEsT0FBTyxDQUFDLFdBQVcsMENBQUUsZ0JBQWdCLEtBQUksRUFBRTtnQkFDN0QsZUFBZSxFQUFFLENBQUEsTUFBQSxPQUFPLENBQUMsV0FBVywwQ0FBRSxlQUFlLEtBQUksQ0FBQztnQkFDMUQsYUFBYSxFQUFFLENBQUEsTUFBQSxPQUFPLENBQUMsV0FBVywwQ0FBRSxhQUFhLEtBQUksQ0FBQzthQUN6RDtZQUVELE1BQU0sRUFBRTtnQkFDSixXQUFXLEVBQUUsQ0FBQSxNQUFBLE1BQUEsT0FBTyxDQUFDLFdBQVcsMENBQUUsYUFBYSwwQ0FBRSxXQUFXLE1BQUksTUFBQSxPQUFPLENBQUMsTUFBTSwwQ0FBRSxXQUFXLENBQUEsSUFBSSxHQUFHLEdBQUcsSUFBSSxHQUFHLElBQUk7Z0JBQ2hILFVBQVUsRUFBRSxDQUFBLE1BQUEsTUFBQSxPQUFPLENBQUMsV0FBVywwQ0FBRSxhQUFhLDBDQUFFLFVBQVUsS0FBSSxDQUFDLENBQUEsTUFBQSxPQUFPLENBQUMsV0FBVywwQ0FBRSxXQUFXLEVBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxXQUFXLENBQUMsV0FBVyxHQUFHLElBQUksR0FBRyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztnQkFDcEosVUFBVSxFQUFFLENBQUEsTUFBQSxNQUFBLE9BQU8sQ0FBQyxXQUFXLDBDQUFFLGFBQWEsMENBQUUsVUFBVSxLQUFJLENBQUM7Z0JBQy9ELFlBQVksRUFBRSxDQUFBLE1BQUEsTUFBQSxPQUFPLENBQUMsV0FBVywwQ0FBRSxhQUFhLDBDQUFFLFFBQVEsTUFBSSxNQUFBLE9BQU8sQ0FBQyxNQUFNLDBDQUFFLFlBQVksQ0FBQSxJQUFJLENBQUM7Z0JBQy9GLGVBQWUsRUFBRSxDQUFBLE1BQUEsTUFBQSxPQUFPLENBQUMsV0FBVywwQ0FBRSxhQUFhLDBDQUFFLFVBQVUsTUFBSSxNQUFBLE9BQU8sQ0FBQyxNQUFNLDBDQUFFLGVBQWUsQ0FBQSxJQUFJLENBQUM7Z0JBQ3ZHLFlBQVksRUFBRSxDQUFBLE1BQUEsTUFBQSxPQUFPLENBQUMsV0FBVywwQ0FBRSxhQUFhLDBDQUFFLE9BQU8sTUFBSSxNQUFBLE9BQU8sQ0FBQyxNQUFNLDBDQUFFLFlBQVksQ0FBQSxJQUFJLENBQUM7Z0JBQzlGLFlBQVksRUFBRSxDQUFBLE1BQUEsTUFBQSxPQUFPLENBQUMsV0FBVywwQ0FBRSxhQUFhLDBDQUFFLE1BQU0sTUFBSSxNQUFBLE9BQU8sQ0FBQyxNQUFNLDBDQUFFLFlBQVksQ0FBQSxJQUFJLENBQUM7Z0JBQzdGLGFBQWEsRUFBRSxDQUFBLE9BQUEsTUFBQSxPQUFPLENBQUMsV0FBVywwQ0FBRSxhQUFhLDRDQUFFLGFBQWEsTUFBSSxPQUFBLE9BQU8sQ0FBQyxNQUFNLDRDQUFFLGFBQWEsQ0FBQSxJQUFJLENBQUM7YUFDekc7WUFFRCxRQUFRLEVBQUU7Z0JBQ04sS0FBSyxFQUFFLENBQUEsT0FBQSxPQUFPLENBQUMsUUFBUSw0Q0FBRSxhQUFhLEtBQUksQ0FBQztnQkFDM0MsTUFBTSxFQUFFLENBQUEsT0FBQSxPQUFPLENBQUMsUUFBUSw0Q0FBRSxjQUFjLEtBQUksQ0FBQztnQkFDN0MsUUFBUSxFQUFFLENBQUMsQ0FBQSxPQUFBLE9BQU8sQ0FBQyxRQUFRLDRDQUFFLGFBQWEsS0FBSSxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUEsT0FBQSxPQUFPLENBQUMsUUFBUSw0Q0FBRSxjQUFjLEtBQUksQ0FBQyxDQUFDO2dCQUMxRixVQUFVLEVBQUUsQ0FBQSxPQUFBLE9BQU8sQ0FBQyxRQUFRLDRDQUFFLFVBQVUsS0FBSSxDQUFDO2dCQUM3QyxhQUFhLEVBQUUsQ0FBQSxPQUFBLE9BQU8sQ0FBQyxRQUFRLDRDQUFFLGFBQWEsS0FBSSxDQUFDO2dCQUNuRCxvQkFBb0IsRUFBRSxDQUFBLE9BQUEsT0FBTyxDQUFDLFFBQVEsNENBQUUsb0JBQW9CLEtBQUksRUFBRTtnQkFDbEUsdUJBQXVCLEVBQUUsQ0FBQSxPQUFBLE9BQU8sQ0FBQyxRQUFRLDRDQUFFLHVCQUF1QixLQUFJLEVBQUU7YUFDM0U7WUFFRCxVQUFVLEVBQUU7Z0JBQ1IsVUFBVSxFQUFFLENBQUEsT0FBQSxPQUFPLENBQUMsVUFBVSw0Q0FBRSxjQUFjLEtBQUksQ0FBQztnQkFDbkQsY0FBYyxFQUFFLENBQUEsT0FBQSxPQUFPLENBQUMsVUFBVSw0Q0FBRSxrQkFBa0IsS0FBSSxDQUFDO2dCQUMzRCxjQUFjLEVBQUUsQ0FBQyxDQUFBLE9BQUEsT0FBTyxDQUFDLFVBQVUsNENBQUUsY0FBYyxLQUFJLEVBQUUsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLElBQVMsRUFBRSxFQUFFLENBQUMsQ0FBQztvQkFDM0UsUUFBUSxFQUFFLElBQUksQ0FBQyxRQUFRO29CQUN2QixhQUFhLEVBQUUsSUFBSSxDQUFDLGFBQWEsSUFBSSxDQUFDO29CQUN0QyxpQkFBaUIsRUFBRSxJQUFJLENBQUMsaUJBQWlCLElBQUksQ0FBQztvQkFDOUMsV0FBVyxFQUFFLElBQUksQ0FBQyxXQUFXLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxJQUFJLENBQUMsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLGlCQUFpQixJQUFJLENBQUMsQ0FBQztvQkFDMUYsUUFBUSxFQUFFLElBQUksQ0FBQyxRQUFRLElBQUksQ0FBQztvQkFDNUIsZUFBZSxFQUFFLElBQUksQ0FBQyxRQUFRLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxhQUFhLEdBQUcsSUFBSSxDQUFDLFFBQVEsR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztpQkFDdEYsQ0FBQyxDQUFDO2FBQ047WUFFRCxPQUFPLEVBQUU7Z0JBQ0wsS0FBSyxFQUFFLENBQUEsT0FBQSxPQUFPLENBQUMsT0FBTyw0Q0FBRSxZQUFZLEtBQUksQ0FBQztnQkFDekMsV0FBVyxFQUFFLENBQUMsQ0FBQSxPQUFBLE9BQU8sQ0FBQyxPQUFPLDRDQUFFLFdBQVcsS0FBSSxFQUFFLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFRLEVBQUUsRUFBRTtvQkFDL0QsTUFBTSxVQUFVLEdBQUcsR0FBRyxDQUFDLElBQUksQ0FBQztvQkFDNUIsTUFBTSxVQUFVLEdBQUcsbUJBQW1CLENBQUMsR0FBRyxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsQ0FBQztvQkFFNUQsT0FBTzt3QkFDSCxJQUFJLEVBQUUsVUFBVTt3QkFDaEIsSUFBSSxFQUFFLEdBQUcsQ0FBQyxJQUFJLElBQUksU0FBUzt3QkFDM0IsV0FBVyxFQUFFLEdBQUcsQ0FBQyxXQUFXLElBQUksQ0FBQzt3QkFDakMsb0JBQW9CLEVBQUUsR0FBRyxDQUFDLGFBQWEsSUFBSSxDQUFDO3dCQUM1QyxnQkFBZ0IsRUFBRSxHQUFHLENBQUMsZ0JBQWdCLElBQUksR0FBRyxDQUFDLGFBQWEsSUFBSSxDQUFDO3dCQUNoRSxnQkFBZ0IsRUFBRSxHQUFHLENBQUMsZ0JBQWdCLElBQUksR0FBRyxDQUFDLGFBQWEsSUFBSSxDQUFDO3dCQUNoRSxvQkFBb0IsRUFBRSxHQUFHLENBQUMsb0JBQW9CLElBQUksRUFBRTt3QkFDcEQsV0FBVyxFQUFFLEdBQUcsQ0FBQyxXQUFXLElBQUksQ0FBQzt3QkFDakMsV0FBVyxFQUFFLEdBQUcsQ0FBQyxXQUFXLElBQUksQ0FBQzt3QkFDakMsT0FBTyxFQUFFLEdBQUcsQ0FBQyxPQUFPLEtBQUssS0FBSzt3QkFDOUIsVUFBVSxFQUFFLFVBQVU7cUJBQ3pCLENBQUM7Z0JBQ04sQ0FBQyxDQUFDO2FBQ0w7WUFFRCxNQUFNLEVBQUU7Z0JBQ0osWUFBWSxFQUFFLE9BQU8sQ0FBQyxZQUFZLElBQUksSUFBSTtnQkFDMUMsV0FBVyxFQUFFLENBQUEsT0FBQSxPQUFPLENBQUMsTUFBTSw0Q0FBRSxXQUFXLEtBQUksQ0FBQztnQkFDN0MsZ0JBQWdCLEVBQUUsQ0FBQSxPQUFBLE9BQU8sQ0FBQyxRQUFRLDRDQUFFLGFBQWEsS0FBSSxDQUFDO2dCQUN0RCxnQkFBZ0IsRUFBRSxDQUFBLE9BQUEsT0FBTyxDQUFDLE9BQU8sNENBQUUsWUFBWSxLQUFJLENBQUM7Z0JBQ3BELFdBQVcsRUFBRSxDQUFBLE9BQUEsT0FBTyxDQUFDLE1BQU0sNENBQUUsV0FBVyxLQUFJLE1BQU07YUFDckQ7U0FDSixDQUFDO0lBQ04sQ0FBQztDQUNKO0FBRUQ7O0dBRUc7QUFDSCxNQUFNLGdCQUFnQixHQUFzQjtJQUN4QyxVQUFVLEVBQUUsRUFBRTtJQUNkLFlBQVksRUFBRSxPQUFPO0lBQ3JCLFNBQVMsRUFBRSxLQUFLO0lBQ2hCLGVBQWUsRUFBRSxLQUFLO0lBQ3RCLFlBQVksRUFBRSxJQUFJO0lBQ2xCLE1BQU0sRUFBRSxDQUFDO0lBQ1QsV0FBVyxFQUFFO1FBQ1QsU0FBUyxFQUFFLENBQUM7UUFDWixHQUFHLEVBQUUsQ0FBQztRQUNOLGdCQUFnQixFQUFFLENBQUM7UUFDbkIsWUFBWSxFQUFFLENBQUM7UUFDZixZQUFZLEVBQUUsQ0FBQztRQUNmLGdCQUFnQixFQUFFLEVBQUU7UUFDcEIsZUFBZSxFQUFFLENBQUM7UUFDbEIsYUFBYSxFQUFFLENBQUM7S0FDbkI7SUFDRCxNQUFNLEVBQUU7UUFDSixXQUFXLEVBQUUsQ0FBQztRQUNkLFVBQVUsRUFBRSxDQUFDO1FBQ2IsVUFBVSxFQUFFLENBQUM7UUFDYixZQUFZLEVBQUUsQ0FBQztRQUNmLGVBQWUsRUFBRSxDQUFDO1FBQ2xCLFlBQVksRUFBRSxDQUFDO1FBQ2YsWUFBWSxFQUFFLENBQUM7UUFDZixhQUFhLEVBQUUsQ0FBQztLQUNuQjtJQUNELFFBQVEsRUFBRTtRQUNOLEtBQUssRUFBRSxDQUFDO1FBQ1IsTUFBTSxFQUFFLENBQUM7UUFDVCxRQUFRLEVBQUUsQ0FBQztRQUNYLFVBQVUsRUFBRSxDQUFDO1FBQ2IsYUFBYSxFQUFFLENBQUM7UUFDaEIsb0JBQW9CLEVBQUUsRUFBRTtRQUN4Qix1QkFBdUIsRUFBRSxFQUFFO0tBQzlCO0lBQ0QsVUFBVSxFQUFFO1FBQ1IsVUFBVSxFQUFFLENBQUM7UUFDYixjQUFjLEVBQUUsQ0FBQztRQUNqQixjQUFjLEVBQUUsRUFBRTtLQUNyQjtJQUNELE9BQU8sRUFBRTtRQUNMLEtBQUssRUFBRSxDQUFDO1FBQ1IsV0FBVyxFQUFFLEVBQUU7S0FDbEI7SUFDRCxNQUFNLEVBQUU7UUFDSixZQUFZLEVBQUUsSUFBSTtRQUNsQixXQUFXLEVBQUUsQ0FBQztRQUNkLGdCQUFnQixFQUFFLENBQUM7UUFDbkIsZ0JBQWdCLEVBQUUsQ0FBQztRQUNuQixXQUFXLEVBQUUsQ0FBQztLQUNqQjtDQUNKLENBQUM7QUFFRixZQUFZO0FBQ1osSUFBSSxpQkFBaUIsR0FBMEIsSUFBSSxDQUFDO0FBRXBEOztHQUVHO0FBQ0gsS0FBSyxVQUFVLGlCQUFpQjtJQUM1QixJQUFJLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztRQUNyQixpQkFBaUIsR0FBRyxJQUFJLGNBQWMsQ0FBQyxJQUFJLENBQUMsQ0FBQztJQUNqRCxDQUFDO0lBRUQsSUFBSSxDQUFDLGlCQUFpQixDQUFDLE9BQU8sRUFBRSxDQUFDO1FBQzdCLE1BQU0saUJBQWlCLENBQUMsS0FBSyxFQUFFLENBQUM7SUFDcEMsQ0FBQztJQUVELE9BQU8saUJBQWlCLENBQUM7QUFDN0IsQ0FBQztBQUVELE1BQU0sQ0FBQyxPQUFPLEdBQUcsTUFBTSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUM7SUFDakMsU0FBUyxFQUFFO1FBQ1AsSUFBSSxLQUFLLENBQUM7UUFDVixJQUFJLEtBQUssQ0FBQztLQUNiO0lBQ0QsUUFBUSxFQUFFLHNCQUFzQjtJQUNoQyxLQUFLLEVBQUUsSUFBQSx1QkFBWSxFQUFDLElBQUEsV0FBSSxFQUFDLFNBQVMsRUFBRSx1Q0FBdUMsQ0FBQyxFQUFFLE9BQU8sQ0FBQztJQUN0RixDQUFDLEVBQUU7UUFDQyxHQUFHLEVBQUUsTUFBTTtLQUNkO0lBQ0QsS0FBSztRQUNELElBQUksSUFBSSxDQUFDLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQztZQUNiLE1BQU0sR0FBRyxHQUFHLElBQUEsZUFBUyxFQUFDLElBQUEscUJBQWUsRUFBQztnQkFDbEMsS0FBSztvQkFDRCxNQUFNLFNBQVMsR0FBRyxJQUFBLGNBQVEsb0JBQXlCLGdCQUFnQixFQUFHLENBQUM7b0JBQ3ZFLE1BQU0sYUFBYSxHQUFHLElBQUEsU0FBRyxFQUFpQixFQUFFLENBQUMsQ0FBQztvQkFDOUMsTUFBTSxrQkFBa0IsR0FBRyxJQUFBLFNBQUcsRUFBUyxFQUFFLENBQUMsQ0FBQztvQkFDM0MsTUFBTSxhQUFhLEdBQUcsSUFBQSxTQUFHLEVBQUMsSUFBSSxDQUFDLENBQUM7b0JBQ2hDLE1BQU0sZUFBZSxHQUFHLElBQUEsU0FBRyxFQUFDLEdBQUcsQ0FBQyxDQUFDO29CQUNqQyxNQUFNLGNBQWMsR0FBRyxJQUFBLFNBQUcsRUFBQyxFQUFFLENBQUMsQ0FBQztvQkFDL0IsTUFBTSxxQkFBcUIsR0FBRyxJQUFBLFNBQUcsRUFBQyxLQUFLLENBQUMsQ0FBQztvQkFFekMsSUFBSSxVQUFVLEdBQTBCLElBQUksQ0FBQztvQkFDN0MsSUFBSSxXQUFXLEdBQTBCLElBQUksQ0FBQztvQkFFOUMsV0FBVztvQkFDWCxNQUFNLGdCQUFnQixHQUFHLEtBQUssSUFBSSxFQUFFO3dCQUNoQyxJQUFJLENBQUM7NEJBQ0QsV0FBVyxHQUFHLE1BQU0saUJBQWlCLEVBQUUsQ0FBQzt3QkFDNUMsQ0FBQzt3QkFBQyxPQUFPLEtBQUssRUFBRSxDQUFDOzRCQUNiLE9BQU8sQ0FBQyxLQUFLLENBQUMsK0JBQStCLEVBQUUsS0FBSyxDQUFDLENBQUM7d0JBQzFELENBQUM7b0JBQ0wsQ0FBQyxDQUFDO29CQUVGLGdCQUFnQjtvQkFDaEIsTUFBTSxlQUFlLEdBQUcsS0FBSyxJQUFJLEVBQUU7d0JBQy9CLElBQUksQ0FBQyxXQUFXOzRCQUFFLE9BQU87d0JBRXpCLElBQUksQ0FBQzs0QkFDRCxTQUFTOzRCQUNULGFBQWEsQ0FBQyxLQUFLLEdBQUcsV0FBVyxDQUFDLFNBQVMsQ0FBQzs0QkFFNUMsbUJBQW1COzRCQUNuQixJQUFJLGtCQUFrQixDQUFDLEtBQUssRUFBRSxDQUFDO2dDQUMzQixNQUFNLFlBQVksR0FBRyxXQUFXLENBQUMsb0JBQW9CLENBQUMsa0JBQWtCLENBQUMsS0FBSyxDQUFDLENBQUM7Z0NBQ2hGLElBQUksWUFBWSxFQUFFLENBQUM7b0NBQ2YsTUFBTSxDQUFDLE1BQU0sQ0FBQyxTQUFTLEVBQUUsWUFBWSxDQUFDLENBQUM7Z0NBQzNDLENBQUM7cUNBQU0sQ0FBQztvQ0FDSixhQUFhO29DQUNiLGtCQUFrQixDQUFDLEtBQUssR0FBRyxFQUFFLENBQUM7b0NBQzlCLE1BQU0sQ0FBQyxNQUFNLENBQUMsU0FBUyxFQUFFLGdCQUFnQixDQUFDLENBQUM7Z0NBQy9DLENBQUM7NEJBQ0wsQ0FBQzs0QkFFRCxjQUFjLENBQUMsS0FBSyxHQUFHLElBQUksSUFBSSxFQUFFLENBQUMsa0JBQWtCLEVBQUUsQ0FBQzt3QkFDM0QsQ0FBQzt3QkFBQyxPQUFPLEtBQUssRUFBRSxDQUFDOzRCQUNiLE9BQU8sQ0FBQyxLQUFLLENBQUMsOEJBQThCLEVBQUUsS0FBSyxDQUFDLENBQUM7d0JBQ3pELENBQUM7b0JBQ0wsQ0FBQyxDQUFDO29CQUVGLFNBQVM7b0JBQ1QsTUFBTSxnQkFBZ0IsR0FBRyxHQUFHLEVBQUU7d0JBQzFCLElBQUksVUFBVTs0QkFBRSxhQUFhLENBQUMsVUFBVSxDQUFDLENBQUM7d0JBRTFDLElBQUksYUFBYSxDQUFDLEtBQUssRUFBRSxDQUFDOzRCQUN0QixVQUFVLEdBQUcsV0FBVyxDQUFDLGVBQWUsRUFBRSxlQUFlLENBQUMsS0FBSyxDQUFDLENBQUM7d0JBQ3JFLENBQUM7b0JBQ0wsQ0FBQyxDQUFDO29CQUVGLFNBQVM7b0JBQ1QsTUFBTSxlQUFlLEdBQUcsR0FBRyxFQUFFO3dCQUN6QixJQUFJLFVBQVUsRUFBRSxDQUFDOzRCQUNiLGFBQWEsQ0FBQyxVQUFVLENBQUMsQ0FBQzs0QkFDMUIsVUFBVSxHQUFHLElBQUksQ0FBQzt3QkFDdEIsQ0FBQztvQkFDTCxDQUFDLENBQUM7b0JBRUYsT0FBTztvQkFDUCxNQUFNLGFBQWEsR0FBRyxHQUFHLEVBQUU7d0JBQ3ZCLGVBQWUsRUFBRSxDQUFDO29CQUN0QixDQUFDLENBQUM7b0JBRUYsU0FBUztvQkFDVCxNQUFNLGlCQUFpQixHQUFHLEdBQUcsRUFBRTt3QkFDM0IsSUFBSSxhQUFhLENBQUMsS0FBSyxFQUFFLENBQUM7NEJBQ3RCLGdCQUFnQixFQUFFLENBQUM7d0JBQ3ZCLENBQUM7NkJBQU0sQ0FBQzs0QkFDSixlQUFlLEVBQUUsQ0FBQzt3QkFDdEIsQ0FBQztvQkFDTCxDQUFDLENBQUM7b0JBRUYsU0FBUztvQkFDVCxNQUFNLHFCQUFxQixHQUFHLEdBQUcsRUFBRTt3QkFDL0IsSUFBSSxhQUFhLENBQUMsS0FBSyxFQUFFLENBQUM7NEJBQ3RCLGdCQUFnQixFQUFFLENBQUM7d0JBQ3ZCLENBQUM7b0JBQ0wsQ0FBQyxDQUFDO29CQUVGLFNBQVM7b0JBQ1QsTUFBTSxpQkFBaUIsR0FBRyxHQUFHLEVBQUU7d0JBQzNCLElBQUksa0JBQWtCLENBQUMsS0FBSyxFQUFFLENBQUM7NEJBQzNCLGVBQWUsRUFBRSxDQUFDO3dCQUN0QixDQUFDOzZCQUFNLENBQUM7NEJBQ0osTUFBTSxDQUFDLE1BQU0sQ0FBQyxTQUFTLEVBQUUsZ0JBQWdCLENBQUMsQ0FBQzt3QkFDL0MsQ0FBQztvQkFDTCxDQUFDLENBQUM7b0JBRUYsVUFBVTtvQkFDVixNQUFNLFlBQVksR0FBRyxDQUFDLE9BQWUsRUFBVSxFQUFFO3dCQUM3QyxNQUFNLEtBQUssR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUMsQ0FBQzt3QkFDekMsTUFBTSxPQUFPLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUMsR0FBRyxFQUFFLENBQUMsQ0FBQzt3QkFDbEQsTUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLEdBQUcsRUFBRSxDQUFDLENBQUM7d0JBQ3RDLE9BQU8sR0FBRyxLQUFLLENBQUMsUUFBUSxFQUFFLENBQUMsUUFBUSxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsSUFBSSxPQUFPLENBQUMsUUFBUSxFQUFFLENBQUMsUUFBUSxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsSUFBSSxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUMsUUFBUSxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsRUFBRSxDQUFDO29CQUM3SCxDQUFDLENBQUM7b0JBRUYsVUFBVTtvQkFDVixNQUFNLFlBQVksR0FBRyxDQUFDLEtBQWEsRUFBVSxFQUFFO3dCQUMzQyxJQUFJLEtBQUssR0FBRyxJQUFJOzRCQUFFLE9BQU8sS0FBSyxHQUFHLElBQUksQ0FBQzt3QkFDdEMsSUFBSSxLQUFLLEdBQUcsSUFBSSxHQUFHLElBQUk7NEJBQUUsT0FBTyxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEdBQUcsS0FBSyxDQUFDO3dCQUNsRSxJQUFJLEtBQUssR0FBRyxJQUFJLEdBQUcsSUFBSSxHQUFHLElBQUk7NEJBQUUsT0FBTyxDQUFDLEtBQUssR0FBRyxDQUFDLElBQUksR0FBRyxJQUFJLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsR0FBRyxLQUFLLENBQUM7d0JBQ2xGLE9BQU8sQ0FBQyxLQUFLLEdBQUcsQ0FBQyxJQUFJLEdBQUcsSUFBSSxHQUFHLElBQUksQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxHQUFHLEtBQUssQ0FBQztvQkFDN0QsQ0FBQyxDQUFDO29CQUVGLFVBQVU7b0JBQ1YsTUFBTSxXQUFXLEdBQUcsQ0FBQyxHQUFXLEVBQVUsRUFBRTt3QkFDeEMsSUFBSSxHQUFHLElBQUksRUFBRTs0QkFBRSxPQUFPLE1BQU0sQ0FBQzt3QkFDN0IsSUFBSSxHQUFHLElBQUksRUFBRTs0QkFBRSxPQUFPLFNBQVMsQ0FBQzt3QkFDaEMsT0FBTyxVQUFVLENBQUM7b0JBQ3RCLENBQUMsQ0FBQztvQkFFRixTQUFTO29CQUNULE1BQU0sY0FBYyxHQUFHLENBQUMsVUFBa0IsRUFBVSxFQUFFO3dCQUNsRCxJQUFJLFVBQVUsR0FBRyxFQUFFOzRCQUFFLE9BQU8sTUFBTSxDQUFDO3dCQUNuQyxJQUFJLFVBQVUsR0FBRyxFQUFFOzRCQUFFLE9BQU8sU0FBUyxDQUFDO3dCQUN0QyxPQUFPLFVBQVUsQ0FBQztvQkFDdEIsQ0FBQyxDQUFDO29CQUVGLGNBQWM7b0JBQ2QsTUFBTSxlQUFlLEdBQUcsQ0FBQyxVQUFrQixFQUFVLEVBQUU7d0JBQ25ELElBQUksQ0FBQyxVQUFVOzRCQUFFLE9BQU8sTUFBTSxDQUFDO3dCQUMvQixJQUFJLFVBQVUsSUFBSSxFQUFFOzRCQUFFLE9BQU8sTUFBTSxDQUFDLENBQUssZ0JBQWdCO3dCQUN6RCxJQUFJLFVBQVUsSUFBSSxFQUFFOzRCQUFFLE9BQU8sU0FBUyxDQUFDLENBQUUsZ0JBQWdCO3dCQUN6RCxPQUFPLFVBQVUsQ0FBQyxDQUF1QixlQUFlO29CQUM1RCxDQUFDLENBQUM7b0JBRUYsV0FBVztvQkFDWCxNQUFNLHFCQUFxQixHQUFHLENBQUMsSUFBWSxFQUFVLEVBQUU7d0JBQ25ELElBQUksSUFBSSxHQUFHLENBQUM7NEJBQUUsT0FBTyxNQUFNLENBQUMsQ0FBRSxVQUFVO3dCQUN4QyxJQUFJLElBQUksR0FBRyxDQUFDOzRCQUFFLE9BQU8sU0FBUyxDQUFDLENBQUMsVUFBVTt3QkFDMUMsT0FBTyxVQUFVLENBQUM7b0JBQ3RCLENBQUMsQ0FBQztvQkFFRixXQUFXO29CQUNYLE1BQU0saUJBQWlCLEdBQUcsQ0FBQyxPQUFlLEVBQVEsRUFBRTt3QkFDaEQsTUFBTSxJQUFJLEdBQTJCOzRCQUNqQyxnQkFBZ0IsRUFBRSw4RkFBOEY7NEJBQ2hILDBCQUEwQixFQUFFLHdGQUF3Rjt5QkFDdkgsQ0FBQzt3QkFFRixNQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUM7d0JBQzFCLElBQUksQ0FBQyxHQUFHOzRCQUFFLE9BQU87d0JBRWpCLElBQUksQ0FBQzs0QkFDRCwyQ0FBMkM7NEJBQzNDLE1BQU0sRUFBRSxLQUFLLEVBQUUsR0FBRyxPQUFPLENBQUMsVUFBVSxDQUFDLENBQUM7NEJBQ3RDLEtBQUssQ0FBQyxZQUFZLENBQUMsR0FBRyxDQUFDLENBQUM7d0JBQzVCLENBQUM7d0JBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQzs0QkFDYixPQUFPLENBQUMsS0FBSyxDQUFDLFNBQVMsRUFBRSxLQUFLLENBQUMsQ0FBQzs0QkFDaEMsY0FBYzs0QkFDZCxrQkFBa0IsQ0FBQyxHQUFHLENBQUMsQ0FBQzt3QkFDNUIsQ0FBQztvQkFDTCxDQUFDLENBQUM7b0JBRUYsZ0JBQWdCO29CQUNoQixNQUFNLGtCQUFrQixHQUFHLENBQUMsR0FBVyxFQUFRLEVBQUU7d0JBQzdDLElBQUksQ0FBQzs0QkFDRCxnQkFBZ0I7NEJBQ2hCLElBQUksU0FBUyxDQUFDLFNBQVMsSUFBSSxTQUFTLENBQUMsU0FBUyxDQUFDLFNBQVMsRUFBRSxDQUFDO2dDQUN2RCxTQUFTLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUMsR0FBRyxFQUFFO29DQUN6QyxPQUFPLENBQUMsR0FBRyxDQUFDLGdCQUFnQixHQUFHLEVBQUUsQ0FBQyxDQUFDO29DQUNuQyxtQkFBbUI7b0NBQ25CLElBQUksT0FBTyxLQUFLLEtBQUssV0FBVyxFQUFFLENBQUM7d0NBQy9CLEtBQUssQ0FBQyw0QkFBNEIsR0FBRyxFQUFFLENBQUMsQ0FBQztvQ0FDN0MsQ0FBQztnQ0FDTCxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsR0FBRyxFQUFFO29DQUNWLGdCQUFnQixDQUFDLEdBQUcsQ0FBQyxDQUFDO2dDQUMxQixDQUFDLENBQUMsQ0FBQzs0QkFDUCxDQUFDO2lDQUFNLENBQUM7Z0NBQ0osZ0JBQWdCLENBQUMsR0FBRyxDQUFDLENBQUM7NEJBQzFCLENBQUM7d0JBQ0wsQ0FBQzt3QkFBQyxPQUFPLEtBQUssRUFBRSxDQUFDOzRCQUNiLGdCQUFnQixDQUFDLEdBQUcsQ0FBQyxDQUFDO3dCQUMxQixDQUFDO29CQUNMLENBQUMsQ0FBQztvQkFFRixZQUFZO29CQUNaLE1BQU0sZ0JBQWdCLEdBQUcsQ0FBQyxJQUFZLEVBQVEsRUFBRTt3QkFDNUMsSUFBSSxDQUFDOzRCQUNELFlBQVk7NEJBQ1osTUFBTSxRQUFRLEdBQUcsUUFBUSxDQUFDLGFBQWEsQ0FBQyxVQUFVLENBQUMsQ0FBQzs0QkFDcEQsUUFBUSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUM7NEJBQ3RCLFFBQVEsQ0FBQyxLQUFLLENBQUMsUUFBUSxHQUFHLE9BQU8sQ0FBQzs0QkFDbEMsUUFBUSxDQUFDLEtBQUssQ0FBQyxJQUFJLEdBQUcsV0FBVyxDQUFDOzRCQUNsQyxRQUFRLENBQUMsS0FBSyxDQUFDLEdBQUcsR0FBRyxXQUFXLENBQUM7NEJBQ2pDLFFBQVEsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLFFBQVEsQ0FBQyxDQUFDOzRCQUNwQyxRQUFRLENBQUMsS0FBSyxFQUFFLENBQUM7NEJBQ2pCLFFBQVEsQ0FBQyxNQUFNLEVBQUUsQ0FBQzs0QkFFbEIsTUFBTSxVQUFVLEdBQUcsUUFBUSxDQUFDLFdBQVcsQ0FBQyxNQUFNLENBQUMsQ0FBQzs0QkFDaEQsUUFBUSxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsUUFBUSxDQUFDLENBQUM7NEJBRXBDLElBQUksVUFBVSxFQUFFLENBQUM7Z0NBQ2IsT0FBTyxDQUFDLEdBQUcsQ0FBQyxnQkFBZ0IsSUFBSSxFQUFFLENBQUMsQ0FBQztnQ0FDcEMsSUFBSSxPQUFPLEtBQUssS0FBSyxXQUFXLEVBQUUsQ0FBQztvQ0FDL0IsS0FBSyxDQUFDLDRCQUE0QixJQUFJLEVBQUUsQ0FBQyxDQUFDO2dDQUM5QyxDQUFDOzRCQUNMLENBQUM7aUNBQU0sQ0FBQztnQ0FDSixPQUFPLENBQUMsR0FBRyxDQUFDLGNBQWMsSUFBSSxFQUFFLENBQUMsQ0FBQztnQ0FDbEMsSUFBSSxPQUFPLEtBQUssS0FBSyxXQUFXLEVBQUUsQ0FBQztvQ0FDL0IsS0FBSyxDQUFDLGVBQWUsSUFBSSxFQUFFLENBQUMsQ0FBQztnQ0FDakMsQ0FBQzs0QkFDTCxDQUFDO3dCQUNMLENBQUM7d0JBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQzs0QkFDYixPQUFPLENBQUMsR0FBRyxDQUFDLFlBQVksSUFBSSxFQUFFLENBQUMsQ0FBQzs0QkFDaEMsSUFBSSxPQUFPLEtBQUssS0FBSyxXQUFXLEVBQUUsQ0FBQztnQ0FDL0IsS0FBSyxDQUFDLGFBQWEsSUFBSSxFQUFFLENBQUMsQ0FBQzs0QkFDL0IsQ0FBQzt3QkFDTCxDQUFDO29CQUNMLENBQUMsQ0FBQztvQkFFRixXQUFXO29CQUNYLElBQUEsZUFBUyxFQUFDLEtBQUssSUFBSSxFQUFFO3dCQUNqQixNQUFNLGdCQUFnQixFQUFFLENBQUM7d0JBQ3pCLGVBQWUsRUFBRSxDQUFDO3dCQUNsQixnQkFBZ0IsRUFBRSxDQUFDO29CQUN2QixDQUFDLENBQUMsQ0FBQztvQkFFSCxVQUFVO29CQUNWLElBQUEsaUJBQVcsRUFBQyxHQUFHLEVBQUU7d0JBQ2IsZUFBZSxFQUFFLENBQUM7b0JBQ3RCLENBQUMsQ0FBQyxDQUFDO29CQUVILE9BQU87d0JBQ0gsU0FBUzt3QkFDVCxhQUFhO3dCQUNiLGtCQUFrQjt3QkFDbEIsYUFBYTt3QkFDYixlQUFlO3dCQUNmLGNBQWM7d0JBQ2QscUJBQXFCO3dCQUNyQixhQUFhO3dCQUNiLGlCQUFpQjt3QkFDakIscUJBQXFCO3dCQUNyQixpQkFBaUI7d0JBQ2pCLFlBQVk7d0JBQ1osWUFBWTt3QkFDWixXQUFXO3dCQUNYLGNBQWM7d0JBQ2QsZUFBZTt3QkFDZixxQkFBcUI7d0JBQ3JCLGlCQUFpQjtxQkFDcEIsQ0FBQztnQkFDTixDQUFDO2dCQUNELFFBQVEsRUFBRSxJQUFBLHVCQUFZLEVBQUMsSUFBQSxXQUFJLEVBQUMsU0FBUyxFQUFFLDJDQUEyQyxDQUFDLEVBQUUsT0FBTyxDQUFDO2FBQ2hHLENBQUMsQ0FBQyxDQUFDO1lBRUosR0FBRyxDQUFDLE1BQU0sQ0FBQyxlQUFlLENBQUMsZUFBZSxHQUFHLENBQUMsR0FBRyxFQUFFLEVBQUUsQ0FBQyxHQUFHLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQzVFLEdBQUcsQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQztZQUN0QixZQUFZLENBQUMsR0FBRyxDQUFDLElBQUksRUFBRSxHQUFHLENBQUMsQ0FBQztRQUNoQyxDQUFDO0lBQ0wsQ0FBQztJQUNELFdBQVcsS0FBSyxDQUFDO0lBQ2pCLEtBQUs7UUFDRCxNQUFNLEdBQUcsR0FBRyxZQUFZLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQ25DLElBQUksR0FBRyxFQUFFLENBQUM7WUFDTixHQUFHLENBQUMsT0FBTyxFQUFFLENBQUM7WUFDZCxZQUFZLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQzlCLENBQUM7UUFFRCxVQUFVO1FBQ1YsSUFBSSxpQkFBaUIsRUFBRSxDQUFDO1lBQ3BCLGlCQUFpQixDQUFDLElBQUksRUFBRSxDQUFDO1lBQ3pCLGlCQUFpQixHQUFHLElBQUksQ0FBQztRQUM3QixDQUFDO0lBQ0wsQ0FBQztDQUNKLENBQUMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJlYWRGaWxlU3luYyB9IGZyb20gJ2ZzLWV4dHJhJztcclxuaW1wb3J0IHsgam9pbiB9IGZyb20gJ3BhdGgnO1xyXG5pbXBvcnQgeyBjcmVhdGVBcHAsIEFwcCwgZGVmaW5lQ29tcG9uZW50LCByZWYsIHJlYWN0aXZlLCBvbk1vdW50ZWQsIG9uVW5tb3VudGVkIH0gZnJvbSAndnVlJztcclxuaW1wb3J0IHsgV2ViU29ja2V0U2VydmVyLCBXZWJTb2NrZXQgfSBmcm9tICd3cyc7XHJcbmltcG9ydCB7IEluY29taW5nTWVzc2FnZSB9IGZyb20gJ2h0dHAnO1xyXG5cclxuY29uc3QgcGFuZWxEYXRhTWFwID0gbmV3IFdlYWtNYXA8YW55LCBBcHA+KCk7XHJcblxyXG4vKipcclxuICog5ri45oiP5a6e5L6L5L+h5oGvXHJcbiAqL1xyXG5pbnRlcmZhY2UgR2FtZUluc3RhbmNlIHtcclxuICAgIGlkOiBzdHJpbmc7XHJcbiAgICBuYW1lOiBzdHJpbmc7XHJcbiAgICBjb25uZWN0VGltZTogbnVtYmVyO1xyXG4gICAgbGFzdFVwZGF0ZVRpbWU6IG51bWJlcjtcclxuICAgIGlzQWN0aXZlOiBib29sZWFuO1xyXG4gICAgZGVidWdEYXRhPzogYW55O1xyXG4gICAgd3M/OiBXZWJTb2NrZXQ7IC8vIFdlYlNvY2tldOi/nuaOpVxyXG59XHJcblxyXG4vKipcclxuICog6K+m57uG55qE6LCD6K+V5L+h5oGv5o6l5Y+jXHJcbiAqL1xyXG5pbnRlcmZhY2UgRGV0YWlsZWREZWJ1Z0luZm8ge1xyXG4gICAgLy8g5Z+656GA5L+h5oGvXHJcbiAgICBpbnN0YW5jZUlkOiBzdHJpbmc7XHJcbiAgICBpbnN0YW5jZU5hbWU6IHN0cmluZztcclxuICAgIGlzUnVubmluZzogYm9vbGVhbjtcclxuICAgIGZyYW1ld29ya0xvYWRlZDogYm9vbGVhbjtcclxuICAgIGN1cnJlbnRTY2VuZTogc3RyaW5nO1xyXG4gICAgdXB0aW1lOiBudW1iZXI7XHJcbiAgICBcclxuICAgIC8vIOaAp+iDveS/oeaBr1xyXG4gICAgcGVyZm9ybWFuY2U6IHtcclxuICAgICAgICBmcmFtZVRpbWU6IG51bWJlcjtcclxuICAgICAgICBmcHM6IG51bWJlcjtcclxuICAgICAgICBhdmVyYWdlRnJhbWVUaW1lOiBudW1iZXI7XHJcbiAgICAgICAgbWluRnJhbWVUaW1lOiBudW1iZXI7XHJcbiAgICAgICAgbWF4RnJhbWVUaW1lOiBudW1iZXI7XHJcbiAgICAgICAgZnJhbWVUaW1lSGlzdG9yeTogbnVtYmVyW107XHJcbiAgICAgICAgZW5naW5lRnJhbWVUaW1lOiBudW1iZXI7XHJcbiAgICAgICAgZWNzUGVyY2VudGFnZTogbnVtYmVyO1xyXG4gICAgfTtcclxuICAgIFxyXG4gICAgLy8g5YaF5a2Y5L+h5oGvXHJcbiAgICBtZW1vcnk6IHtcclxuICAgICAgICB0b3RhbE1lbW9yeTogbnVtYmVyO1xyXG4gICAgICAgIHVzZWRNZW1vcnk6IG51bWJlcjtcclxuICAgICAgICBmcmVlTWVtb3J5OiBudW1iZXI7XHJcbiAgICAgICAgZW50aXR5TWVtb3J5OiBudW1iZXI7XHJcbiAgICAgICAgY29tcG9uZW50TWVtb3J5OiBudW1iZXI7XHJcbiAgICAgICAgc3lzdGVtTWVtb3J5OiBudW1iZXI7XHJcbiAgICAgICAgcG9vbGVkTWVtb3J5OiBudW1iZXI7XHJcbiAgICAgICAgZ2NDb2xsZWN0aW9uczogbnVtYmVyO1xyXG4gICAgfTtcclxuICAgIFxyXG4gICAgLy8g5a6e5L2T5L+h5oGvXHJcbiAgICBlbnRpdGllczoge1xyXG4gICAgICAgIHRvdGFsOiBudW1iZXI7XHJcbiAgICAgICAgYWN0aXZlOiBudW1iZXI7XHJcbiAgICAgICAgaW5hY3RpdmU6IG51bWJlcjtcclxuICAgICAgICBwZW5kaW5nQWRkOiBudW1iZXI7XHJcbiAgICAgICAgcGVuZGluZ1JlbW92ZTogbnVtYmVyO1xyXG4gICAgICAgIGVudGl0aWVzUGVyQXJjaGV0eXBlOiBBcnJheTx7XHJcbiAgICAgICAgICAgIHNpZ25hdHVyZTogc3RyaW5nO1xyXG4gICAgICAgICAgICBjb3VudDogbnVtYmVyO1xyXG4gICAgICAgICAgICBtZW1vcnk6IG51bWJlcjtcclxuICAgICAgICB9PjtcclxuICAgICAgICB0b3BFbnRpdGllc0J5Q29tcG9uZW50czogQXJyYXk8e1xyXG4gICAgICAgICAgICBpZDogc3RyaW5nO1xyXG4gICAgICAgICAgICBuYW1lOiBzdHJpbmc7XHJcbiAgICAgICAgICAgIGNvbXBvbmVudENvdW50OiBudW1iZXI7XHJcbiAgICAgICAgICAgIG1lbW9yeTogbnVtYmVyO1xyXG4gICAgICAgIH0+O1xyXG4gICAgfTtcclxuICAgIFxyXG4gICAgLy8g57uE5Lu25L+h5oGvXHJcbiAgICBjb21wb25lbnRzOiB7XHJcbiAgICAgICAgdG90YWxUeXBlczogbnVtYmVyO1xyXG4gICAgICAgIHRvdGFsSW5zdGFuY2VzOiBudW1iZXI7XHJcbiAgICAgICAgY29tcG9uZW50U3RhdHM6IEFycmF5PHtcclxuICAgICAgICAgICAgdHlwZU5hbWU6IHN0cmluZztcclxuICAgICAgICAgICAgaW5zdGFuY2VDb3VudDogbnVtYmVyO1xyXG4gICAgICAgICAgICBtZW1vcnlQZXJJbnN0YW5jZTogbnVtYmVyO1xyXG4gICAgICAgICAgICB0b3RhbE1lbW9yeTogbnVtYmVyO1xyXG4gICAgICAgICAgICBwb29sU2l6ZTogbnVtYmVyO1xyXG4gICAgICAgICAgICBwb29sVXRpbGl6YXRpb246IG51bWJlcjtcclxuICAgICAgICB9PjtcclxuICAgIH07XHJcbiAgICBcclxuICAgIC8vIOezu+e7n+S/oeaBr1xyXG4gICAgc3lzdGVtczoge1xyXG4gICAgICAgIHRvdGFsOiBudW1iZXI7XHJcbiAgICAgICAgc3lzdGVtU3RhdHM6IEFycmF5PHtcclxuICAgICAgICAgICAgbmFtZTogc3RyaW5nO1xyXG4gICAgICAgICAgICB0eXBlOiBzdHJpbmc7XHJcbiAgICAgICAgICAgIGVudGl0eUNvdW50OiBudW1iZXI7XHJcbiAgICAgICAgICAgIGF2ZXJhZ2VFeGVjdXRpb25UaW1lOiBudW1iZXI7XHJcbiAgICAgICAgICAgIG1pbkV4ZWN1dGlvblRpbWU6IG51bWJlcjtcclxuICAgICAgICAgICAgbWF4RXhlY3V0aW9uVGltZTogbnVtYmVyO1xyXG4gICAgICAgICAgICBleGVjdXRpb25UaW1lSGlzdG9yeTogbnVtYmVyW107XHJcbiAgICAgICAgICAgIG1lbW9yeVVzYWdlOiBudW1iZXI7XHJcbiAgICAgICAgICAgIHVwZGF0ZU9yZGVyOiBudW1iZXI7XHJcbiAgICAgICAgICAgIGVuYWJsZWQ6IGJvb2xlYW47XHJcbiAgICAgICAgICAgIHBlcmNlbnRhZ2U6IG51bWJlcjtcclxuICAgICAgICB9PjtcclxuICAgIH07XHJcbiAgICBcclxuICAgIC8vIOWcuuaZr+S/oeaBr1xyXG4gICAgc2NlbmVzOiB7XHJcbiAgICAgICAgY3VycmVudFNjZW5lOiBzdHJpbmc7XHJcbiAgICAgICAgc2NlbmVNZW1vcnk6IG51bWJlcjtcclxuICAgICAgICBzY2VuZUVudGl0eUNvdW50OiBudW1iZXI7XHJcbiAgICAgICAgc2NlbmVTeXN0ZW1Db3VudDogbnVtYmVyO1xyXG4gICAgICAgIHNjZW5lVXB0aW1lOiBudW1iZXI7XHJcbiAgICB9O1xyXG59XHJcblxyXG4vKipcclxuICogRUNT6LCD6K+V5pyN5Yqh5ZmoXHJcbiAqIOS9nOS4uuacjeWKoeerr++8jOaOpeaUtuWkmuS4qua4uOaIj+WunuS+i+eahOi/nuaOpVxyXG4gKi9cclxuY2xhc3MgRUNTRGVidWdTZXJ2ZXIge1xyXG4gICAgcHJpdmF0ZSB3c3M/OiBXZWJTb2NrZXRTZXJ2ZXI7XHJcbiAgICBwcml2YXRlIHBvcnQ6IG51bWJlciA9IDgwODA7XHJcbiAgICBwcml2YXRlIGdhbWVJbnN0YW5jZXMgPSBuZXcgTWFwPHN0cmluZywgR2FtZUluc3RhbmNlPigpO1xyXG4gICAgcHJpdmF0ZSBpc1J1bm5pbmc6IGJvb2xlYW4gPSBmYWxzZTtcclxuXHJcbiAgICBjb25zdHJ1Y3Rvcihwb3J0OiBudW1iZXIgPSA4MDgwKSB7XHJcbiAgICAgICAgdGhpcy5wb3J0ID0gcG9ydDtcclxuICAgIH1cclxuXHJcbiAgICBhc3luYyBzdGFydCgpOiBQcm9taXNlPGJvb2xlYW4+IHtcclxuICAgICAgICBpZiAodGhpcy5pc1J1bm5pbmcpIHJldHVybiB0cnVlO1xyXG5cclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICB0aGlzLndzcyA9IG5ldyBXZWJTb2NrZXRTZXJ2ZXIoeyBwb3J0OiB0aGlzLnBvcnQgfSk7XHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICB0aGlzLndzcy5vbignY29ubmVjdGlvbicsICh3czogV2ViU29ja2V0LCByZXE6IEluY29taW5nTWVzc2FnZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgaW5zdGFuY2VJZCA9IHRoaXMuZ2VuZXJhdGVJbnN0YW5jZUlkKCk7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBpbnN0YW5jZTogR2FtZUluc3RhbmNlID0ge1xyXG4gICAgICAgICAgICAgICAgICAgIGlkOiBpbnN0YW5jZUlkLFxyXG4gICAgICAgICAgICAgICAgICAgIG5hbWU6IGDmuLjmiI/lrp7kvostJHtpbnN0YW5jZUlkLnN1YnN0cmluZygwLCA4KX1gLFxyXG4gICAgICAgICAgICAgICAgICAgIGNvbm5lY3RUaW1lOiBEYXRlLm5vdygpLFxyXG4gICAgICAgICAgICAgICAgICAgIGxhc3RVcGRhdGVUaW1lOiBEYXRlLm5vdygpLFxyXG4gICAgICAgICAgICAgICAgICAgIGlzQWN0aXZlOiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgIGRlYnVnRGF0YTogbnVsbCxcclxuICAgICAgICAgICAgICAgICAgICB3czogd3NcclxuICAgICAgICAgICAgICAgIH07XHJcblxyXG4gICAgICAgICAgICAgICAgdGhpcy5nYW1lSW5zdGFuY2VzLnNldChpbnN0YW5jZUlkLCBpbnN0YW5jZSk7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgW0VDUyBEZWJ1ZyBTZXJ2ZXJdIE5ldyBpbnN0YW5jZSBjb25uZWN0ZWQ6ICR7aW5zdGFuY2UubmFtZX1gKTtcclxuXHJcbiAgICAgICAgICAgICAgICB3cy5vbignbWVzc2FnZScsIChkYXRhOiBCdWZmZXIpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBtZXNzYWdlID0gSlNPTi5wYXJzZShkYXRhLnRvU3RyaW5nKCkpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmhhbmRsZU1lc3NhZ2UoaW5zdGFuY2VJZCwgbWVzc2FnZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignW0VDUyBEZWJ1ZyBTZXJ2ZXJdIEZhaWxlZCB0byBwYXJzZSBtZXNzYWdlOicsIGVycm9yKTtcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICAgICAgICB3cy5vbignY2xvc2UnLCAoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaW5zdGFuY2UgPSB0aGlzLmdhbWVJbnN0YW5jZXMuZ2V0KGluc3RhbmNlSWQpO1xyXG4gICAgICAgICAgICAgICAgICAgIGlmIChpbnN0YW5jZSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpbnN0YW5jZS5pc0FjdGl2ZSA9IGZhbHNlO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgW0VDUyBEZWJ1ZyBTZXJ2ZXJdIEluc3RhbmNlIGRpc2Nvbm5lY3RlZDogJHtpbnN0YW5jZS5uYW1lfWApO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgICAgICAgIHdzLm9uKCdlcnJvcicsIChlcnJvcjogRXJyb3IpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKGBbRUNTIERlYnVnIFNlcnZlcl0gV2ViU29ja2V0IGVycm9yIGZvciAke2luc3RhbmNlSWR9OmAsIGVycm9yKTtcclxuICAgICAgICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgICAgICAgIC8vIOWPkemAgei/nuaOpeehruiupFxyXG4gICAgICAgICAgICAgICAgdGhpcy5zZW5kVG9JbnN0YW5jZShpbnN0YW5jZUlkLCB7XHJcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2Nvbm5lY3Rpb25fY29uZmlybWVkJyxcclxuICAgICAgICAgICAgICAgICAgICBpbnN0YW5jZUlkOiBpbnN0YW5jZUlkLFxyXG4gICAgICAgICAgICAgICAgICAgIHNlcnZlclRpbWU6IERhdGUubm93KClcclxuICAgICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICAgIHRoaXMuaXNSdW5uaW5nID0gdHJ1ZTtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coYFtFQ1MgRGVidWcgU2VydmVyXSBTdGFydGVkIG9uIHBvcnQgJHt0aGlzLnBvcnR9YCk7XHJcbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xyXG5cclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdbRUNTIERlYnVnIFNlcnZlcl0gRmFpbGVkIHRvIHN0YXJ0OicsIGVycm9yKTtcclxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBzdG9wKCk6IHZvaWQge1xyXG4gICAgICAgIGlmICh0aGlzLndzcykge1xyXG4gICAgICAgICAgICB0aGlzLndzcy5jbG9zZSgpO1xyXG4gICAgICAgICAgICB0aGlzLndzcyA9IHVuZGVmaW5lZDtcclxuICAgICAgICB9XHJcbiAgICAgICAgdGhpcy5nYW1lSW5zdGFuY2VzLmNsZWFyKCk7XHJcbiAgICAgICAgdGhpcy5pc1J1bm5pbmcgPSBmYWxzZTtcclxuICAgICAgICBjb25zb2xlLmxvZygnW0VDUyBEZWJ1ZyBTZXJ2ZXJdIFN0b3BwZWQnKTtcclxuICAgIH1cclxuXHJcbiAgICBwcml2YXRlIGdlbmVyYXRlSW5zdGFuY2VJZCgpOiBzdHJpbmcge1xyXG4gICAgICAgIHJldHVybiBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoMikgKyBEYXRlLm5vdygpLnRvU3RyaW5nKDM2KTtcclxuICAgIH1cclxuXHJcbiAgICBwcml2YXRlIGhhbmRsZU1lc3NhZ2UoaW5zdGFuY2VJZDogc3RyaW5nLCBtZXNzYWdlOiBhbnkpOiB2b2lkIHtcclxuICAgICAgICBjb25zdCBpbnN0YW5jZSA9IHRoaXMuZ2FtZUluc3RhbmNlcy5nZXQoaW5zdGFuY2VJZCk7XHJcbiAgICAgICAgaWYgKCFpbnN0YW5jZSkgcmV0dXJuO1xyXG5cclxuICAgICAgICBzd2l0Y2ggKG1lc3NhZ2UudHlwZSkge1xyXG4gICAgICAgICAgICBjYXNlICdkZWJ1Z19kYXRhJzpcclxuICAgICAgICAgICAgICAgIGluc3RhbmNlLmRlYnVnRGF0YSA9IG1lc3NhZ2UuZGF0YTtcclxuICAgICAgICAgICAgICAgIGluc3RhbmNlLmxhc3RVcGRhdGVUaW1lID0gRGF0ZS5ub3coKTtcclxuICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIGNhc2UgJ2luc3RhbmNlX2luZm8nOlxyXG4gICAgICAgICAgICAgICAgaWYgKG1lc3NhZ2UubmFtZSkge1xyXG4gICAgICAgICAgICAgICAgICAgIGluc3RhbmNlLm5hbWUgPSBtZXNzYWdlLm5hbWU7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICBjYXNlICdwaW5nJzpcclxuICAgICAgICAgICAgICAgIHRoaXMuc2VuZFRvSW5zdGFuY2UoaW5zdGFuY2VJZCwgeyB0eXBlOiAncG9uZycsIHRpbWVzdGFtcDogRGF0ZS5ub3coKSB9KTtcclxuICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBwcml2YXRlIHNlbmRUb0luc3RhbmNlKGluc3RhbmNlSWQ6IHN0cmluZywgbWVzc2FnZTogYW55KTogdm9pZCB7XHJcbiAgICAgICAgY29uc3QgaW5zdGFuY2UgPSB0aGlzLmdhbWVJbnN0YW5jZXMuZ2V0KGluc3RhbmNlSWQpO1xyXG4gICAgICAgIGlmIChpbnN0YW5jZSAmJiBpbnN0YW5jZS53cyAmJiBpbnN0YW5jZS53cy5yZWFkeVN0YXRlID09PSAxKSB7XHJcbiAgICAgICAgICAgIGluc3RhbmNlLndzLnNlbmQoSlNPTi5zdHJpbmdpZnkobWVzc2FnZSkpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBnZXQgcnVubmluZygpOiBib29sZWFuIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5pc1J1bm5pbmc7XHJcbiAgICB9XHJcblxyXG4gICAgZ2V0IGluc3RhbmNlcygpOiBHYW1lSW5zdGFuY2VbXSB7XHJcbiAgICAgICAgcmV0dXJuIEFycmF5LmZyb20odGhpcy5nYW1lSW5zdGFuY2VzLnZhbHVlcygpKTtcclxuICAgIH1cclxuXHJcbiAgICBnZXRJbnN0YW5jZShpbnN0YW5jZUlkOiBzdHJpbmcpOiBHYW1lSW5zdGFuY2UgfCB1bmRlZmluZWQge1xyXG4gICAgICAgIHJldHVybiB0aGlzLmdhbWVJbnN0YW5jZXMuZ2V0KGluc3RhbmNlSWQpO1xyXG4gICAgfVxyXG5cclxuICAgIGdldEluc3RhbmNlRGVidWdEYXRhKGluc3RhbmNlSWQ6IHN0cmluZyk6IERldGFpbGVkRGVidWdJbmZvIHwgbnVsbCB7XHJcbiAgICAgICAgY29uc3QgaW5zdGFuY2UgPSB0aGlzLmdhbWVJbnN0YW5jZXMuZ2V0KGluc3RhbmNlSWQpO1xyXG4gICAgICAgIGlmICghaW5zdGFuY2UgfHwgIWluc3RhbmNlLmRlYnVnRGF0YSkge1xyXG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHJldHVybiB0aGlzLnRyYW5zZm9ybVRvRGV0YWlsZWREZWJ1Z0luZm8oaW5zdGFuY2UsIGluc3RhbmNlLmRlYnVnRGF0YSk7XHJcbiAgICB9XHJcblxyXG4gICAgcHJpdmF0ZSB0cmFuc2Zvcm1Ub0RldGFpbGVkRGVidWdJbmZvKGluc3RhbmNlOiBHYW1lSW5zdGFuY2UsIHJhd0RhdGE6IGFueSk6IERldGFpbGVkRGVidWdJbmZvIHtcclxuICAgICAgICBjb25zdCB1cHRpbWUgPSAoRGF0ZS5ub3coKSAtIGluc3RhbmNlLmNvbm5lY3RUaW1lKSAvIDEwMDA7XHJcbiAgICAgICAgXHJcbiAgICAgICAgLy8g6K6h566X57O757uf5oCn6IO95pWw5o2u77yM5YyF5ousRUNT5Y2g5q+UXHJcbiAgICAgICAgY29uc3Qgc3lzdGVtQnJlYWtkb3duID0gcmF3RGF0YS5wZXJmb3JtYW5jZT8uc3lzdGVtQnJlYWtkb3duIHx8IFtdO1xyXG4gICAgICAgIGNvbnN0IHN5c3RlbVBlcmZvcm1hbmNlID0gcmF3RGF0YS5wZXJmb3JtYW5jZT8uc3lzdGVtUGVyZm9ybWFuY2UgfHwgW107XHJcbiAgICAgICAgXHJcbiAgICAgICAgLy8g5Yib5bu657O757uf5ZCN56ew5Yiw5Y2g5q+U55qE5pig5bCEXHJcbiAgICAgICAgY29uc3Qgc3lzdGVtUGVyY2VudGFnZU1hcCA9IG5ldyBNYXA8c3RyaW5nLCBudW1iZXI+KCk7XHJcbiAgICAgICAgc3lzdGVtQnJlYWtkb3duLmZvckVhY2goKHN5czogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIHN5c3RlbVBlcmNlbnRhZ2VNYXAuc2V0KHN5cy5zeXN0ZW1OYW1lLCBzeXMucGVyY2VudGFnZSB8fCAwKTtcclxuICAgICAgICB9KTtcclxuICAgICAgICBcclxuICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICBpbnN0YW5jZUlkOiBpbnN0YW5jZS5pZCxcclxuICAgICAgICAgICAgaW5zdGFuY2VOYW1lOiBpbnN0YW5jZS5uYW1lLFxyXG4gICAgICAgICAgICBpc1J1bm5pbmc6IHJhd0RhdGEuaXNSdW5uaW5nIHx8IGZhbHNlLFxyXG4gICAgICAgICAgICBmcmFtZXdvcmtMb2FkZWQ6IHJhd0RhdGEuZnJhbWV3b3JrTG9hZGVkIHx8IGZhbHNlLFxyXG4gICAgICAgICAgICBjdXJyZW50U2NlbmU6IHJhd0RhdGEuY3VycmVudFNjZW5lIHx8ICfmnKrnn6UnLFxyXG4gICAgICAgICAgICB1cHRpbWU6IHVwdGltZSxcclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIHBlcmZvcm1hbmNlOiB7XHJcbiAgICAgICAgICAgICAgICBmcmFtZVRpbWU6IHJhd0RhdGEucGVyZm9ybWFuY2U/LmZyYW1lVGltZSB8fCAwLFxyXG4gICAgICAgICAgICAgICAgZnBzOiByYXdEYXRhLnBlcmZvcm1hbmNlPy5mcHMgfHwgMCxcclxuICAgICAgICAgICAgICAgIGF2ZXJhZ2VGcmFtZVRpbWU6IHJhd0RhdGEucGVyZm9ybWFuY2U/LmF2ZXJhZ2VGcmFtZVRpbWUgfHwgcmF3RGF0YS5wZXJmb3JtYW5jZT8uZnJhbWVUaW1lIHx8IDAsXHJcbiAgICAgICAgICAgICAgICBtaW5GcmFtZVRpbWU6IHJhd0RhdGEucGVyZm9ybWFuY2U/Lm1pbkZyYW1lVGltZSB8fCByYXdEYXRhLnBlcmZvcm1hbmNlPy5mcmFtZVRpbWUgfHwgMCxcclxuICAgICAgICAgICAgICAgIG1heEZyYW1lVGltZTogcmF3RGF0YS5wZXJmb3JtYW5jZT8ubWF4RnJhbWVUaW1lIHx8IHJhd0RhdGEucGVyZm9ybWFuY2U/LmZyYW1lVGltZSB8fCAwLFxyXG4gICAgICAgICAgICAgICAgZnJhbWVUaW1lSGlzdG9yeTogcmF3RGF0YS5wZXJmb3JtYW5jZT8uZnJhbWVUaW1lSGlzdG9yeSB8fCBbXSxcclxuICAgICAgICAgICAgICAgIGVuZ2luZUZyYW1lVGltZTogcmF3RGF0YS5wZXJmb3JtYW5jZT8uZW5naW5lRnJhbWVUaW1lIHx8IDAsXHJcbiAgICAgICAgICAgICAgICBlY3NQZXJjZW50YWdlOiByYXdEYXRhLnBlcmZvcm1hbmNlPy5lY3NQZXJjZW50YWdlIHx8IDBcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIG1lbW9yeToge1xyXG4gICAgICAgICAgICAgICAgdG90YWxNZW1vcnk6IHJhd0RhdGEucGVyZm9ybWFuY2U/Lm1lbW9yeURldGFpbHM/LnRvdGFsTWVtb3J5IHx8IHJhd0RhdGEubWVtb3J5Py50b3RhbE1lbW9yeSB8fCA1MTIgKiAxMDI0ICogMTAyNCxcclxuICAgICAgICAgICAgICAgIHVzZWRNZW1vcnk6IHJhd0RhdGEucGVyZm9ybWFuY2U/Lm1lbW9yeURldGFpbHM/LnVzZWRNZW1vcnkgfHwgKHJhd0RhdGEucGVyZm9ybWFuY2U/Lm1lbW9yeVVzYWdlID8gcmF3RGF0YS5wZXJmb3JtYW5jZS5tZW1vcnlVc2FnZSAqIDEwMjQgKiAxMDI0IDogMCksXHJcbiAgICAgICAgICAgICAgICBmcmVlTWVtb3J5OiByYXdEYXRhLnBlcmZvcm1hbmNlPy5tZW1vcnlEZXRhaWxzPy5mcmVlTWVtb3J5IHx8IDAsXHJcbiAgICAgICAgICAgICAgICBlbnRpdHlNZW1vcnk6IHJhd0RhdGEucGVyZm9ybWFuY2U/Lm1lbW9yeURldGFpbHM/LmVudGl0aWVzIHx8IHJhd0RhdGEubWVtb3J5Py5lbnRpdHlNZW1vcnkgfHwgMCxcclxuICAgICAgICAgICAgICAgIGNvbXBvbmVudE1lbW9yeTogcmF3RGF0YS5wZXJmb3JtYW5jZT8ubWVtb3J5RGV0YWlscz8uY29tcG9uZW50cyB8fCByYXdEYXRhLm1lbW9yeT8uY29tcG9uZW50TWVtb3J5IHx8IDAsXHJcbiAgICAgICAgICAgICAgICBzeXN0ZW1NZW1vcnk6IHJhd0RhdGEucGVyZm9ybWFuY2U/Lm1lbW9yeURldGFpbHM/LnN5c3RlbXMgfHwgcmF3RGF0YS5tZW1vcnk/LnN5c3RlbU1lbW9yeSB8fCAwLFxyXG4gICAgICAgICAgICAgICAgcG9vbGVkTWVtb3J5OiByYXdEYXRhLnBlcmZvcm1hbmNlPy5tZW1vcnlEZXRhaWxzPy5wb29sZWQgfHwgcmF3RGF0YS5tZW1vcnk/LnBvb2xlZE1lbW9yeSB8fCAwLFxyXG4gICAgICAgICAgICAgICAgZ2NDb2xsZWN0aW9uczogcmF3RGF0YS5wZXJmb3JtYW5jZT8ubWVtb3J5RGV0YWlscz8uZ2NDb2xsZWN0aW9ucyB8fCByYXdEYXRhLm1lbW9yeT8uZ2NDb2xsZWN0aW9ucyB8fCAwXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICBlbnRpdGllczoge1xyXG4gICAgICAgICAgICAgICAgdG90YWw6IHJhd0RhdGEuZW50aXRpZXM/LnRvdGFsRW50aXRpZXMgfHwgMCxcclxuICAgICAgICAgICAgICAgIGFjdGl2ZTogcmF3RGF0YS5lbnRpdGllcz8uYWN0aXZlRW50aXRpZXMgfHwgMCxcclxuICAgICAgICAgICAgICAgIGluYWN0aXZlOiAocmF3RGF0YS5lbnRpdGllcz8udG90YWxFbnRpdGllcyB8fCAwKSAtIChyYXdEYXRhLmVudGl0aWVzPy5hY3RpdmVFbnRpdGllcyB8fCAwKSxcclxuICAgICAgICAgICAgICAgIHBlbmRpbmdBZGQ6IHJhd0RhdGEuZW50aXRpZXM/LnBlbmRpbmdBZGQgfHwgMCxcclxuICAgICAgICAgICAgICAgIHBlbmRpbmdSZW1vdmU6IHJhd0RhdGEuZW50aXRpZXM/LnBlbmRpbmdSZW1vdmUgfHwgMCxcclxuICAgICAgICAgICAgICAgIGVudGl0aWVzUGVyQXJjaGV0eXBlOiByYXdEYXRhLmVudGl0aWVzPy5lbnRpdGllc1BlckFyY2hldHlwZSB8fCBbXSxcclxuICAgICAgICAgICAgICAgIHRvcEVudGl0aWVzQnlDb21wb25lbnRzOiByYXdEYXRhLmVudGl0aWVzPy50b3BFbnRpdGllc0J5Q29tcG9uZW50cyB8fCBbXVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBcclxuICAgICAgICAgICAgY29tcG9uZW50czoge1xyXG4gICAgICAgICAgICAgICAgdG90YWxUeXBlczogcmF3RGF0YS5jb21wb25lbnRzPy5jb21wb25lbnRUeXBlcyB8fCAwLFxyXG4gICAgICAgICAgICAgICAgdG90YWxJbnN0YW5jZXM6IHJhd0RhdGEuY29tcG9uZW50cz8uY29tcG9uZW50SW5zdGFuY2VzIHx8IDAsXHJcbiAgICAgICAgICAgICAgICBjb21wb25lbnRTdGF0czogKHJhd0RhdGEuY29tcG9uZW50cz8uY29tcG9uZW50U3RhdHMgfHwgW10pLm1hcCgoY29tcDogYW55KSA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgIHR5cGVOYW1lOiBjb21wLnR5cGVOYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgIGluc3RhbmNlQ291bnQ6IGNvbXAuaW5zdGFuY2VDb3VudCB8fCAwLFxyXG4gICAgICAgICAgICAgICAgICAgIG1lbW9yeVBlckluc3RhbmNlOiBjb21wLm1lbW9yeVBlckluc3RhbmNlIHx8IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgdG90YWxNZW1vcnk6IGNvbXAudG90YWxNZW1vcnkgfHwgKGNvbXAuaW5zdGFuY2VDb3VudCB8fCAwKSAqIChjb21wLm1lbW9yeVBlckluc3RhbmNlIHx8IDApLFxyXG4gICAgICAgICAgICAgICAgICAgIHBvb2xTaXplOiBjb21wLnBvb2xTaXplIHx8IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgcG9vbFV0aWxpemF0aW9uOiBjb21wLnBvb2xTaXplID4gMCA/IChjb21wLmluc3RhbmNlQ291bnQgLyBjb21wLnBvb2xTaXplICogMTAwKSA6IDBcclxuICAgICAgICAgICAgICAgIH0pKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBcclxuICAgICAgICAgICAgc3lzdGVtczoge1xyXG4gICAgICAgICAgICAgICAgdG90YWw6IHJhd0RhdGEuc3lzdGVtcz8udG90YWxTeXN0ZW1zIHx8IDAsXHJcbiAgICAgICAgICAgICAgICBzeXN0ZW1TdGF0czogKHJhd0RhdGEuc3lzdGVtcz8uc3lzdGVtc0luZm8gfHwgW10pLm1hcCgoc3lzOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBzeXN0ZW1OYW1lID0gc3lzLm5hbWU7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcGVyY2VudGFnZSA9IHN5c3RlbVBlcmNlbnRhZ2VNYXAuZ2V0KHN5c3RlbU5hbWUpIHx8IDA7XHJcbiAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgbmFtZTogc3lzdGVtTmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogc3lzLnR5cGUgfHwgJ1Vua25vd24nLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBlbnRpdHlDb3VudDogc3lzLmVudGl0eUNvdW50IHx8IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGF2ZXJhZ2VFeGVjdXRpb25UaW1lOiBzeXMuZXhlY3V0aW9uVGltZSB8fCAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtaW5FeGVjdXRpb25UaW1lOiBzeXMubWluRXhlY3V0aW9uVGltZSB8fCBzeXMuZXhlY3V0aW9uVGltZSB8fCAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtYXhFeGVjdXRpb25UaW1lOiBzeXMubWF4RXhlY3V0aW9uVGltZSB8fCBzeXMuZXhlY3V0aW9uVGltZSB8fCAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBleGVjdXRpb25UaW1lSGlzdG9yeTogc3lzLmV4ZWN1dGlvblRpbWVIaXN0b3J5IHx8IFtdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtZW1vcnlVc2FnZTogc3lzLm1lbW9yeVVzYWdlIHx8IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZU9yZGVyOiBzeXMudXBkYXRlT3JkZXIgfHwgMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgZW5hYmxlZDogc3lzLmVuYWJsZWQgIT09IGZhbHNlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBwZXJjZW50YWdlOiBwZXJjZW50YWdlXHJcbiAgICAgICAgICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICBzY2VuZXM6IHtcclxuICAgICAgICAgICAgICAgIGN1cnJlbnRTY2VuZTogcmF3RGF0YS5jdXJyZW50U2NlbmUgfHwgJ+acquefpScsXHJcbiAgICAgICAgICAgICAgICBzY2VuZU1lbW9yeTogcmF3RGF0YS5zY2VuZXM/LnNjZW5lTWVtb3J5IHx8IDAsXHJcbiAgICAgICAgICAgICAgICBzY2VuZUVudGl0eUNvdW50OiByYXdEYXRhLmVudGl0aWVzPy50b3RhbEVudGl0aWVzIHx8IDAsXHJcbiAgICAgICAgICAgICAgICBzY2VuZVN5c3RlbUNvdW50OiByYXdEYXRhLnN5c3RlbXM/LnRvdGFsU3lzdGVtcyB8fCAwLFxyXG4gICAgICAgICAgICAgICAgc2NlbmVVcHRpbWU6IHJhd0RhdGEuc2NlbmVzPy5zY2VuZVVwdGltZSB8fCB1cHRpbWVcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH07XHJcbiAgICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiDpu5jorqTosIPor5Xkv6Hmga9cclxuICovXHJcbmNvbnN0IGRlZmF1bHREZWJ1Z0luZm86IERldGFpbGVkRGVidWdJbmZvID0ge1xyXG4gICAgaW5zdGFuY2VJZDogJycsXHJcbiAgICBpbnN0YW5jZU5hbWU6ICfmnKrpgInmi6nlrp7kvosnLFxyXG4gICAgaXNSdW5uaW5nOiBmYWxzZSxcclxuICAgIGZyYW1ld29ya0xvYWRlZDogZmFsc2UsXHJcbiAgICBjdXJyZW50U2NlbmU6ICfmnKrnn6UnLFxyXG4gICAgdXB0aW1lOiAwLFxyXG4gICAgcGVyZm9ybWFuY2U6IHtcclxuICAgICAgICBmcmFtZVRpbWU6IDAsXHJcbiAgICAgICAgZnBzOiAwLFxyXG4gICAgICAgIGF2ZXJhZ2VGcmFtZVRpbWU6IDAsXHJcbiAgICAgICAgbWluRnJhbWVUaW1lOiAwLFxyXG4gICAgICAgIG1heEZyYW1lVGltZTogMCxcclxuICAgICAgICBmcmFtZVRpbWVIaXN0b3J5OiBbXSxcclxuICAgICAgICBlbmdpbmVGcmFtZVRpbWU6IDAsXHJcbiAgICAgICAgZWNzUGVyY2VudGFnZTogMFxyXG4gICAgfSxcclxuICAgIG1lbW9yeToge1xyXG4gICAgICAgIHRvdGFsTWVtb3J5OiAwLFxyXG4gICAgICAgIHVzZWRNZW1vcnk6IDAsXHJcbiAgICAgICAgZnJlZU1lbW9yeTogMCxcclxuICAgICAgICBlbnRpdHlNZW1vcnk6IDAsXHJcbiAgICAgICAgY29tcG9uZW50TWVtb3J5OiAwLFxyXG4gICAgICAgIHN5c3RlbU1lbW9yeTogMCxcclxuICAgICAgICBwb29sZWRNZW1vcnk6IDAsXHJcbiAgICAgICAgZ2NDb2xsZWN0aW9uczogMFxyXG4gICAgfSxcclxuICAgIGVudGl0aWVzOiB7XHJcbiAgICAgICAgdG90YWw6IDAsXHJcbiAgICAgICAgYWN0aXZlOiAwLFxyXG4gICAgICAgIGluYWN0aXZlOiAwLFxyXG4gICAgICAgIHBlbmRpbmdBZGQ6IDAsXHJcbiAgICAgICAgcGVuZGluZ1JlbW92ZTogMCxcclxuICAgICAgICBlbnRpdGllc1BlckFyY2hldHlwZTogW10sXHJcbiAgICAgICAgdG9wRW50aXRpZXNCeUNvbXBvbmVudHM6IFtdXHJcbiAgICB9LFxyXG4gICAgY29tcG9uZW50czoge1xyXG4gICAgICAgIHRvdGFsVHlwZXM6IDAsXHJcbiAgICAgICAgdG90YWxJbnN0YW5jZXM6IDAsXHJcbiAgICAgICAgY29tcG9uZW50U3RhdHM6IFtdXHJcbiAgICB9LFxyXG4gICAgc3lzdGVtczoge1xyXG4gICAgICAgIHRvdGFsOiAwLFxyXG4gICAgICAgIHN5c3RlbVN0YXRzOiBbXVxyXG4gICAgfSxcclxuICAgIHNjZW5lczoge1xyXG4gICAgICAgIGN1cnJlbnRTY2VuZTogJ+acquefpScsXHJcbiAgICAgICAgc2NlbmVNZW1vcnk6IDAsXHJcbiAgICAgICAgc2NlbmVFbnRpdHlDb3VudDogMCxcclxuICAgICAgICBzY2VuZVN5c3RlbUNvdW50OiAwLFxyXG4gICAgICAgIHNjZW5lVXB0aW1lOiAwXHJcbiAgICB9XHJcbn07XHJcblxyXG4vLyDlhajlsYDosIPor5XmnI3liqHlmajlrp7kvotcclxubGV0IGdsb2JhbERlYnVnU2VydmVyOiBFQ1NEZWJ1Z1NlcnZlciB8IG51bGwgPSBudWxsO1xyXG5cclxuLyoqXHJcbiAqIOWQr+WKqOiwg+ivleacjeWKoeWZqFxyXG4gKi9cclxuYXN5bmMgZnVuY3Rpb24gZW5zdXJlRGVidWdTZXJ2ZXIoKTogUHJvbWlzZTxFQ1NEZWJ1Z1NlcnZlcj4ge1xyXG4gICAgaWYgKCFnbG9iYWxEZWJ1Z1NlcnZlcikge1xyXG4gICAgICAgIGdsb2JhbERlYnVnU2VydmVyID0gbmV3IEVDU0RlYnVnU2VydmVyKDgwODApO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBpZiAoIWdsb2JhbERlYnVnU2VydmVyLnJ1bm5pbmcpIHtcclxuICAgICAgICBhd2FpdCBnbG9iYWxEZWJ1Z1NlcnZlci5zdGFydCgpO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICByZXR1cm4gZ2xvYmFsRGVidWdTZXJ2ZXI7XHJcbn1cclxuXHJcbm1vZHVsZS5leHBvcnRzID0gRWRpdG9yLlBhbmVsLmRlZmluZSh7XHJcbiAgICBsaXN0ZW5lcnM6IHtcclxuICAgICAgICBzaG93KCkgeyB9LFxyXG4gICAgICAgIGhpZGUoKSB7IH0sXHJcbiAgICB9LFxyXG4gICAgdGVtcGxhdGU6IGA8ZGl2IGlkPVwiYXBwXCI+PC9kaXY+YCxcclxuICAgIHN0eWxlOiByZWFkRmlsZVN5bmMoam9pbihfX2Rpcm5hbWUsICcuLi8uLi8uLi9zdGF0aWMvc3R5bGUvZGVidWcvaW5kZXguY3NzJyksICd1dGYtOCcpLFxyXG4gICAgJDoge1xyXG4gICAgICAgIGFwcDogJyNhcHAnLFxyXG4gICAgfSxcclxuICAgIHJlYWR5KCkge1xyXG4gICAgICAgIGlmICh0aGlzLiQuYXBwKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGFwcCA9IGNyZWF0ZUFwcChkZWZpbmVDb21wb25lbnQoe1xyXG4gICAgICAgICAgICAgICAgc2V0dXAoKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZGVidWdJbmZvID0gcmVhY3RpdmU8RGV0YWlsZWREZWJ1Z0luZm8+KHsgLi4uZGVmYXVsdERlYnVnSW5mbyB9KTtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBnYW1lSW5zdGFuY2VzID0gcmVmPEdhbWVJbnN0YW5jZVtdPihbXSk7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc2VsZWN0ZWRJbnN0YW5jZUlkID0gcmVmPHN0cmluZz4oJycpO1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzQXV0b1JlZnJlc2ggPSByZWYodHJ1ZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcmVmcmVzaEludGVydmFsID0gcmVmKDEwMCk7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbGFzdFVwZGF0ZVRpbWUgPSByZWYoJycpO1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHNob3dDb21wb25lbnRQb29sSGVscCA9IHJlZihmYWxzZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgbGV0IGludGVydmFsSWQ6IE5vZGVKUy5UaW1lb3V0IHwgbnVsbCA9IG51bGw7XHJcbiAgICAgICAgICAgICAgICAgICAgbGV0IGRlYnVnU2VydmVyOiBFQ1NEZWJ1Z1NlcnZlciB8IG51bGwgPSBudWxsO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyDliJ3lp4vljJbosIPor5XmnI3liqHlmahcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBpbml0aWFsaXplU2VydmVyID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVidWdTZXJ2ZXIgPSBhd2FpdCBlbnN1cmVEZWJ1Z1NlcnZlcigpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHN0YXJ0IGRlYnVnIHNlcnZlcjonLCBlcnJvcik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9O1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyDmm7TmlrDmuLjmiI/lrp7kvovliJfooajlkozosIPor5Xkv6Hmga9cclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB1cGRhdGVEZWJ1Z0luZm8gPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghZGVidWdTZXJ2ZXIpIHJldHVybjtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDmm7TmlrDlrp7kvovliJfooahcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdhbWVJbnN0YW5jZXMudmFsdWUgPSBkZWJ1Z1NlcnZlci5pbnN0YW5jZXM7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOWmguaenOaciemAieS4reeahOWunuS+i++8jOabtOaWsOWFtuiwg+ivleS/oeaBr1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHNlbGVjdGVkSW5zdGFuY2VJZC52YWx1ZSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGRldGFpbGVkSW5mbyA9IGRlYnVnU2VydmVyLmdldEluc3RhbmNlRGVidWdEYXRhKHNlbGVjdGVkSW5zdGFuY2VJZC52YWx1ZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGRldGFpbGVkSW5mbykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBPYmplY3QuYXNzaWduKGRlYnVnSW5mbywgZGV0YWlsZWRJbmZvKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDlrp7kvovlt7Lmlq3lvIDvvIzph43nva7pgInmi6lcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRJbnN0YW5jZUlkLnZhbHVlID0gJyc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE9iamVjdC5hc3NpZ24oZGVidWdJbmZvLCBkZWZhdWx0RGVidWdJbmZvKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhc3RVcGRhdGVUaW1lLnZhbHVlID0gbmV3IERhdGUoKS50b0xvY2FsZVRpbWVTdHJpbmcoKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byB1cGRhdGUgZGVidWcgaW5mbzonLCBlcnJvcik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9O1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyDlvIDlp4voh6rliqjliLfmlrBcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBzdGFydEF1dG9SZWZyZXNoID0gKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoaW50ZXJ2YWxJZCkgY2xlYXJJbnRlcnZhbChpbnRlcnZhbElkKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChpc0F1dG9SZWZyZXNoLnZhbHVlKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbnRlcnZhbElkID0gc2V0SW50ZXJ2YWwodXBkYXRlRGVidWdJbmZvLCByZWZyZXNoSW50ZXJ2YWwudmFsdWUpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8g5YGc5q2i6Ieq5Yqo5Yi35pawXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3RvcEF1dG9SZWZyZXNoID0gKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoaW50ZXJ2YWxJZCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xlYXJJbnRlcnZhbChpbnRlcnZhbElkKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGludGVydmFsSWQgPSBudWxsO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8g5omL5Yqo5Yi35pawXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbWFudWFsUmVmcmVzaCA9ICgpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlRGVidWdJbmZvKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgfTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8g5YiH5o2i6Ieq5Yqo5Yi35pawXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdG9nZ2xlQXV0b1JlZnJlc2ggPSAoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChpc0F1dG9SZWZyZXNoLnZhbHVlKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGFydEF1dG9SZWZyZXNoKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdG9wQXV0b1JlZnJlc2goKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH07XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC8vIOabtOaUueWIt+aWsOmXtOmalFxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGNoYW5nZVJlZnJlc2hJbnRlcnZhbCA9ICgpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGlzQXV0b1JlZnJlc2gudmFsdWUpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXJ0QXV0b1JlZnJlc2goKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH07XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC8vIOWunuS+i+mAieaLqeaUueWPmFxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG9uSW5zdGFuY2VDaGFuZ2VkID0gKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoc2VsZWN0ZWRJbnN0YW5jZUlkLnZhbHVlKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVEZWJ1Z0luZm8oKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIE9iamVjdC5hc3NpZ24oZGVidWdJbmZvLCBkZWZhdWx0RGVidWdJbmZvKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH07XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC8vIOagvOW8j+WMlui/kOihjOaXtumXtFxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGZvcm1hdFVwdGltZSA9IChzZWNvbmRzOiBudW1iZXIpOiBzdHJpbmcgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBob3VycyA9IE1hdGguZmxvb3Ioc2Vjb25kcyAvIDM2MDApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBtaW51dGVzID0gTWF0aC5mbG9vcigoc2Vjb25kcyAlIDM2MDApIC8gNjApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBzZWNzID0gTWF0aC5mbG9vcihzZWNvbmRzICUgNjApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gYCR7aG91cnMudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfToke21pbnV0ZXMudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfToke3NlY3MudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfWA7XHJcbiAgICAgICAgICAgICAgICAgICAgfTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8g5qC85byP5YyW5YaF5a2Y5aSn5bCPXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZm9ybWF0TWVtb3J5ID0gKGJ5dGVzOiBudW1iZXIpOiBzdHJpbmcgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoYnl0ZXMgPCAxMDI0KSByZXR1cm4gYnl0ZXMgKyAnIEInO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoYnl0ZXMgPCAxMDI0ICogMTAyNCkgcmV0dXJuIChieXRlcyAvIDEwMjQpLnRvRml4ZWQoMSkgKyAnIEtCJztcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGJ5dGVzIDwgMTAyNCAqIDEwMjQgKiAxMDI0KSByZXR1cm4gKGJ5dGVzIC8gKDEwMjQgKiAxMDI0KSkudG9GaXhlZCgxKSArICcgTUInO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKGJ5dGVzIC8gKDEwMjQgKiAxMDI0ICogMTAyNCkpLnRvRml4ZWQoMSkgKyAnIEdCJztcclxuICAgICAgICAgICAgICAgICAgICB9O1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyDojrflj5ZGUFPpopzoibJcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBnZXRGcHNDb2xvciA9IChmcHM6IG51bWJlcik6IHN0cmluZyA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChmcHMgPj0gNTUpIHJldHVybiAnZ29vZCc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChmcHMgPj0gMzApIHJldHVybiAnd2FybmluZyc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAnY3JpdGljYWwnO1xyXG4gICAgICAgICAgICAgICAgICAgIH07XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC8vIOiOt+WPluWGheWtmOminOiJslxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGdldE1lbW9yeUNvbG9yID0gKHBlcmNlbnRhZ2U6IG51bWJlcik6IHN0cmluZyA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChwZXJjZW50YWdlIDwgNzApIHJldHVybiAnZ29vZCc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChwZXJjZW50YWdlIDwgODUpIHJldHVybiAnd2FybmluZyc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAnY3JpdGljYWwnO1xyXG4gICAgICAgICAgICAgICAgICAgIH07XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC8vIOiOt+WPlkVDU+aXtumXtOWNoOavlOminOiJslxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGdldEVDU1RpbWVDb2xvciA9IChwZXJjZW50YWdlOiBudW1iZXIpOiBzdHJpbmcgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIXBlcmNlbnRhZ2UpIHJldHVybiAnZ29vZCc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChwZXJjZW50YWdlIDw9IDEwKSByZXR1cm4gJ2dvb2QnOyAgICAgLy8gRUNT5Y2g55SoPD0xMCXkuLrnu7/oibJcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHBlcmNlbnRhZ2UgPD0gMzApIHJldHVybiAnd2FybmluZyc7ICAvLyBFQ1PljaDnlKg8PTMwJeS4uum7hOiJslxyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gJ2NyaXRpY2FsJzsgICAgICAgICAgICAgICAgICAgICAgIC8vIEVDU+WNoOeUqD4zMCXkuLrnuqLoibJcclxuICAgICAgICAgICAgICAgICAgICB9O1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyDojrflj5bmiafooYzml7bpl7TpopzoibJcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBnZXRFeGVjdXRpb25UaW1lQ29sb3IgPSAodGltZTogbnVtYmVyKTogc3RyaW5nID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHRpbWUgPCAxKSByZXR1cm4gJ2dvb2QnOyAgLy8gPDFtc+S4uue7v+iJslxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAodGltZSA8IDUpIHJldHVybiAnd2FybmluZyc7IC8vIDw1bXPkuLrpu4ToibJcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuICdjcml0aWNhbCc7XHJcbiAgICAgICAgICAgICAgICAgICAgfTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8g5omT5byA5paH5qGj6ZO+5o6lICBcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBvcGVuRG9jdW1lbnRhdGlvbiA9IChzZWN0aW9uOiBzdHJpbmcpOiB2b2lkID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdXJsczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICdjb21wb25lbnQtcG9vbCc6ICdodHRwczovL2dpdGh1Yi5jb20vZXNlbmdpbmUvZWNzLWZyYW1ld29yay90cmVlL21hc3Rlci9kb2NzL2NvbXBvbmVudC1kZXNpZ24tZ3VpZGUubWQjMS3lr7nosaHmsaDkvJjljJYnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJ3BlcmZvcm1hbmNlLW9wdGltaXphdGlvbic6ICdodHRwczovL2dpdGh1Yi5jb20vZXNlbmdpbmUvZWNzLWZyYW1ld29yay90cmVlL21hc3Rlci9kb2NzL3BlcmZvcm1hbmNlLW9wdGltaXphdGlvbi5tZCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHVybCA9IHVybHNbc2VjdGlvbl07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghdXJsKSByZXR1cm47XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5ZyoQ29jb3MgQ3JlYXRvcuaJqeWxleeOr+Wig+S4re+8jOebtOaOpeS9v+eUqEVsZWN0cm9u55qEc2hlbGzmqKHlnZdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgc2hlbGwgfSA9IHJlcXVpcmUoJ2VsZWN0cm9uJyk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaGVsbC5vcGVuRXh0ZXJuYWwodXJsKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aXoOazleaJk+W8gOmTvuaOpTonLCBlcnJvcik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDlpoLmnpzlpLHotKXvvIzlpI3liLbliLDliarotLTmnb9cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvcHlVcmxUb0NsaXBib2FyZCh1cmwpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8g5aSN5Yi26ZO+5o6l5Yiw5Ymq6LS05p2/55qE6L6F5Yqp5Ye95pWwXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgY29weVVybFRvQ2xpcGJvYXJkID0gKHVybDogc3RyaW5nKTogdm9pZCA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDlsJ3or5Xkvb/nlKjnjrDku6PnmoTliarotLTmnb9BUElcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChuYXZpZ2F0b3IuY2xpcGJvYXJkICYmIG5hdmlnYXRvci5jbGlwYm9hcmQud3JpdGVUZXh0KSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmF2aWdhdG9yLmNsaXBib2FyZC53cml0ZVRleHQodXJsKS50aGVuKCgpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coYOaWh+aho+mTvuaOpeW3suWkjeWItuWIsOWJqui0tOadvzogJHt1cmx9YCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOWmguaenOWPr+iDveeahOivne+8jOaYvuekuueUqOaIt+WPi+WlveeahOaPkOekulxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodHlwZW9mIGFsZXJ0ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxlcnQoYOaWh+aho+mTvuaOpeW3suWkjeWItuWIsOWJqui0tOadv++8jOivt+WcqOa1j+iniOWZqOS4reeymOi0tOiuv+mXrjpcXG4ke3VybH1gKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmFsbGJhY2tDb3B5VGV4dCh1cmwpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmYWxsYmFja0NvcHlUZXh0KHVybCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmYWxsYmFja0NvcHlUZXh0KHVybCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9O1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyDlpIfnlKjnmoTlpI3liLbmlofmnKzmlrnms5VcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBmYWxsYmFja0NvcHlUZXh0ID0gKHRleHQ6IHN0cmluZyk6IHZvaWQgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5Yib5bu65Li05pe255qE5paH5pys5Yy65Z+fXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB0ZXh0QXJlYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3RleHRhcmVhJyk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0QXJlYS52YWx1ZSA9IHRleHQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0QXJlYS5zdHlsZS5wb3NpdGlvbiA9ICdmaXhlZCc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0QXJlYS5zdHlsZS5sZWZ0ID0gJy05OTk5OTlweCc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0QXJlYS5zdHlsZS50b3AgPSAnLTk5OTk5OXB4JztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQodGV4dEFyZWEpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGV4dEFyZWEuZm9jdXMoKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRleHRBcmVhLnNlbGVjdCgpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBzdWNjZXNzZnVsID0gZG9jdW1lbnQuZXhlY0NvbW1hbmQoJ2NvcHknKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQodGV4dEFyZWEpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoc3VjY2Vzc2Z1bCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDmlofmoaPpk77mjqXlt7LlpI3liLbliLDliarotLTmnb86ICR7dGV4dH1gKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodHlwZW9mIGFsZXJ0ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGVydChg5paH5qGj6ZO+5o6l5bey5aSN5Yi25Yiw5Ymq6LS05p2/77yM6K+35Zyo5rWP6KeI5Zmo5Lit57KY6LS06K6/6ZeuOlxcbiR7dGV4dH1gKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDor7fmiYvliqjlpI3liLbmlofmoaPpk77mjqU6ICR7dGV4dH1gKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodHlwZW9mIGFsZXJ0ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGVydChg6K+35omL5Yqo5aSN5Yi25paH5qGj6ZO+5o6lOlxcbiR7dGV4dH1gKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhg6K+35omL5Yqo6K6/6Zeu5paH5qGjOiAke3RleHR9YCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodHlwZW9mIGFsZXJ0ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsZXJ0KGDor7fmiYvliqjorr/pl67mlofmoaM6XFxuJHt0ZXh0fWApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8g57uE5Lu25oyC6L295pe25Yid5aeL5YyWXHJcbiAgICAgICAgICAgICAgICAgICAgb25Nb3VudGVkKGFzeW5jICgpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYXdhaXQgaW5pdGlhbGl6ZVNlcnZlcigpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVEZWJ1Z0luZm8oKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3RhcnRBdXRvUmVmcmVzaCgpO1xyXG4gICAgICAgICAgICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAvLyDnu4Tku7bljbjovb3ml7bmuIXnkIZcclxuICAgICAgICAgICAgICAgICAgICBvblVubW91bnRlZCgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0b3BBdXRvUmVmcmVzaCgpO1xyXG4gICAgICAgICAgICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBkZWJ1Z0luZm8sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGdhbWVJbnN0YW5jZXMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkSW5zdGFuY2VJZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgaXNBdXRvUmVmcmVzaCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmVmcmVzaEludGVydmFsLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBsYXN0VXBkYXRlVGltZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2hvd0NvbXBvbmVudFBvb2xIZWxwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtYW51YWxSZWZyZXNoLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0b2dnbGVBdXRvUmVmcmVzaCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2hhbmdlUmVmcmVzaEludGVydmFsLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkluc3RhbmNlQ2hhbmdlZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgZm9ybWF0VXB0aW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBmb3JtYXRNZW1vcnksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGdldEZwc0NvbG9yLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBnZXRNZW1vcnlDb2xvcixcclxuICAgICAgICAgICAgICAgICAgICAgICAgZ2V0RUNTVGltZUNvbG9yLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBnZXRFeGVjdXRpb25UaW1lQ29sb3IsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9wZW5Eb2N1bWVudGF0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICB0ZW1wbGF0ZTogcmVhZEZpbGVTeW5jKGpvaW4oX19kaXJuYW1lLCAnLi4vLi4vLi4vc3RhdGljL3RlbXBsYXRlL2RlYnVnL2luZGV4Lmh0bWwnKSwgJ3V0Zi04JyksXHJcbiAgICAgICAgICAgIH0pKTtcclxuXHJcbiAgICAgICAgICAgIGFwcC5jb25maWcuY29tcGlsZXJPcHRpb25zLmlzQ3VzdG9tRWxlbWVudCA9ICh0YWcpID0+IHRhZy5zdGFydHNXaXRoKCd1aS0nKTtcclxuICAgICAgICAgICAgYXBwLm1vdW50KHRoaXMuJC5hcHApO1xyXG4gICAgICAgICAgICBwYW5lbERhdGFNYXAuc2V0KHRoaXMsIGFwcCk7XHJcbiAgICAgICAgfVxyXG4gICAgfSxcclxuICAgIGJlZm9yZUNsb3NlKCkgeyB9LFxyXG4gICAgY2xvc2UoKSB7XHJcbiAgICAgICAgY29uc3QgYXBwID0gcGFuZWxEYXRhTWFwLmdldCh0aGlzKTtcclxuICAgICAgICBpZiAoYXBwKSB7XHJcbiAgICAgICAgICAgIGFwcC51bm1vdW50KCk7XHJcbiAgICAgICAgICAgIHBhbmVsRGF0YU1hcC5kZWxldGUodGhpcyk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIFxyXG4gICAgICAgIC8vIOWFs+mXreiwg+ivleacjeWKoeWZqFxyXG4gICAgICAgIGlmIChnbG9iYWxEZWJ1Z1NlcnZlcikge1xyXG4gICAgICAgICAgICBnbG9iYWxEZWJ1Z1NlcnZlci5zdG9wKCk7XHJcbiAgICAgICAgICAgIGdsb2JhbERlYnVnU2VydmVyID0gbnVsbDtcclxuICAgICAgICB9XHJcbiAgICB9LFxyXG59KTsgIl19