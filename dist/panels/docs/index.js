"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const fs_extra_1 = require("fs-extra");
const path_1 = require("path");
/**
 * 文档分类
 */
const DOC_CATEGORIES = {
    'getting-started': '🚀 新手入门',
    'core-concepts': '🏗️ 核心概念',
    'advanced': '🔧 高级功能',
    'performance': '⚡ 性能优化',
    'examples': '🎮 示例应用'
};
/**
 * 文档列表配置
 */
const DOCS_CONFIG = [
    {
        id: 'beginner-tutorials',
        title: '新手教程完整指南',
        filename: 'beginner-tutorials.md',
        category: 'getting-started',
        description: '🎯 专为新手设计的完整学习路径，从零基础到熟练使用',
        size: '7.5KB',
        difficulty: 'beginner',
        recommended: true
    },
    {
        id: 'getting-started',
        title: '快速入门指南',
        filename: 'getting-started.md',
        category: 'getting-started',
        description: '⚡ 5分钟学会ECS框架基础用法，快速开始您的游戏开发',
        size: '19KB',
        difficulty: 'beginner',
        recommended: true
    },
    {
        id: 'core-concepts',
        title: 'ECS核心概念详解',
        filename: 'core-concepts.md',
        category: 'core-concepts',
        description: '📖 深入理解实体、组件、系统三大核心概念',
        size: '16KB',
        difficulty: 'intermediate'
    },
    {
        id: 'concepts-explained',
        title: '技术概念通俗解释',
        filename: 'concepts-explained.md',
        category: 'core-concepts',
        description: '🔍 用简单易懂的语言解释脏标记、Archetype等技术概念',
        size: '19KB',
        difficulty: 'intermediate',
        recommended: true
    },
    {
        id: 'entity-guide',
        title: '实体管理完整指南',
        filename: 'entity-guide.md',
        category: 'core-concepts',
        description: '🎯 掌握实体创建、销毁、查询等核心操作',
        size: '8.6KB',
        difficulty: 'intermediate'
    },
    {
        id: 'system-guide',
        title: '系统开发详细教程',
        filename: 'system-guide.md',
        category: 'core-concepts',
        description: '⚙️ 四种系统类型的完整使用指南和最佳实践',
        size: '15KB',
        difficulty: 'intermediate'
    },
    {
        id: 'component-design-guide',
        title: '组件设计最佳实践',
        filename: 'component-design-guide.md',
        category: 'core-concepts',
        description: '🎨 学会设计高效、可维护的组件架构',
        size: '18KB',
        difficulty: 'intermediate'
    },
    {
        id: 'timer-guide',
        title: '定时器系统完整教程',
        filename: 'timer-guide.md',
        category: 'advanced',
        description: '⏰ 掌握定时器、延迟执行、技能冷却等高级功能',
        size: '19KB',
        difficulty: 'advanced'
    },
    {
        id: 'scene-management-guide',
        title: '场景管理深度指南',
        filename: 'scene-management-guide.md',
        category: 'advanced',
        description: '🎬 学会场景切换、数据传递、状态管理等高级技巧',
        size: '22KB',
        difficulty: 'advanced'
    },
    {
        id: 'performance-optimization',
        title: '性能优化实战指南',
        filename: 'performance-optimization.md',
        category: 'performance',
        description: '🚀 提升游戏性能的实用技巧和优化策略',
        size: '12KB',
        difficulty: 'advanced',
        recommended: true
    },
    {
        id: 'bitmask-guide',
        title: '位掩码使用指南',
        filename: 'bitmask-guide.md',
        category: 'performance',
        description: '💾 理解和使用位掩码进行高性能实体查询',
        size: '14KB',
        difficulty: 'advanced'
    },
    {
        id: 'query-system-usage',
        title: '查询系统使用手册',
        filename: 'query-system-usage.md',
        category: 'advanced',
        description: '🔍 掌握强大的实体查询和过滤功能',
        size: '9.5KB',
        difficulty: 'intermediate'
    },
    {
        id: 'entity-manager-example',
        title: '实体管理器实例教程',
        filename: 'entity-manager-example.md',
        category: 'examples',
        description: '💼 通过实际案例学习实体管理器的使用',
        size: '9.9KB',
        difficulty: 'intermediate'
    },
    {
        id: 'event-system-example',
        title: '事件系统应用示例',
        filename: 'event-system-example.md',
        category: 'examples',
        description: '📡 学会使用类型安全的事件系统进行组件通信',
        size: '13KB',
        difficulty: 'intermediate'
    },
    {
        id: 'use-cases',
        title: '使用场景案例分析',
        filename: 'use-cases.md',
        category: 'examples',
        description: '🎮 从小型游戏到MMO的具体应用案例',
        size: '18KB',
        difficulty: 'beginner'
    }
];
// 全局变量
let currentDoc = null;
let currentCategory = 'all';
let searchQuery = '';
/**
 * 读取文档内容
 */
function readDocContent(filename) {
    try {
        // 在Cocos Creator扩展中，从编译后的dist目录需要回到源码目录
        const docPath = (0, path_1.join)(__dirname, '../../../static/docs', filename);
        console.log('[Window] [ECS Extension] 读取文档路径: ' + docPath);
        // 明确指定UTF-8编码
        const content = (0, fs_extra_1.readFileSync)(docPath, { encoding: 'utf8' });
        console.log('[Window] [ECS Extension] 读取文档成功: ' + filename + ', 长度: ' + content.length + ', 前100字符: ' + content.substring(0, 100));
        return content;
    }
    catch (error) {
        console.error('Failed to read doc file: ' + filename, error);
        // 尝试备用路径（更深一层）
        try {
            const altDocPath = (0, path_1.join)(__dirname, '../../../../static/docs', filename);
            console.log('[Window] [ECS Extension] 尝试备用路径: ' + altDocPath);
            const content = (0, fs_extra_1.readFileSync)(altDocPath, { encoding: 'utf8' });
            console.log('[Window] [ECS Extension] 备用路径读取成功: ' + filename);
            return content;
        }
        catch (altError) {
            console.error('Backup path also failed: ' + altError);
            return '# 文档加载失败\n\n无法加载文档文件: ' + filename + '\n\n主路径错误: ' + (error instanceof Error ? error.message : String(error)) + '\n\n备用路径错误: ' + (altError instanceof Error ? altError.message : String(altError));
        }
    }
}
/**
 * 安全的TypeScript语法高亮
 */
function applyTypescriptSyntaxHighlighting(code) {
    if (!code || code.trim() === '') {
        return code;
    }
    // 如果代码已经包含HTML标签，不再处理
    if (code.includes('<span')) {
        return code;
    }
    // 使用令牌化方法，避免正则表达式冲突
    const tokens = tokenizeCode(code);
    return tokens.map(token => {
        switch (token.type) {
            case 'comment':
                return `<span class="comment">${escapeHtml(token.value)}</span>`;
            case 'string':
                return `<span class="string">${escapeHtml(token.value)}</span>`;
            case 'keyword':
                return `<span class="keyword">${escapeHtml(token.value)}</span>`;
            case 'number':
                return `<span class="number">${escapeHtml(token.value)}</span>`;
            case 'function':
                return `<span class="function">${escapeHtml(token.value)}</span>`;
            default:
                return escapeHtml(token.value);
        }
    }).join('');
}
/**
 * HTML转义
 */
function escapeHtml(text) {
    return text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');
}
/**
 * 代码令牌化
 */
function tokenizeCode(code) {
    const tokens = [];
    let i = 0;
    const keywords = new Set([
        'import', 'export', 'default', 'from', 'as', 'class', 'interface', 'type', 'enum',
        'function', 'const', 'let', 'var', 'return', 'if', 'else', 'for', 'while', 'do',
        'switch', 'case', 'break', 'continue', 'try', 'catch', 'finally', 'throw', 'new',
        'this', 'super', 'extends', 'implements', 'public', 'private', 'protected', 'static',
        'readonly', 'async', 'await', 'true', 'false', 'null', 'undefined', 'void', 'any',
        'string', 'number', 'boolean', 'object', 'Array', 'Promise'
    ]);
    while (i < code.length) {
        const char = code[i];
        // 处理注释
        if (char === '/' && i + 1 < code.length && code[i + 1] === '/') {
            const start = i;
            while (i < code.length && code[i] !== '\n') {
                i++;
            }
            tokens.push({ type: 'comment', value: code.slice(start, i) });
            continue;
        }
        // 处理字符串
        if (char === '"' || char === "'") {
            const quote = char;
            const start = i;
            i++; // 跳过开始引号
            while (i < code.length && code[i] !== quote) {
                if (code[i] === '\\' && i + 1 < code.length) {
                    i += 2; // 跳过转义字符
                }
                else {
                    i++;
                }
            }
            if (i < code.length)
                i++; // 跳过结束引号
            tokens.push({ type: 'string', value: code.slice(start, i) });
            continue;
        }
        // 处理模板字符串（简化处理，保持原样显示）
        if (char === '`') {
            const start = i;
            i++; // 跳过开始反引号
            while (i < code.length && code[i] !== '`') {
                if (code[i] === '\\' && i + 1 < code.length) {
                    i += 2; // 跳过转义字符
                }
                else {
                    i++;
                }
            }
            if (i < code.length)
                i++; // 跳过结束反引号
            tokens.push({ type: 'string', value: code.slice(start, i) });
            continue;
        }
        // 处理数字
        if (/\d/.test(char)) {
            const start = i;
            while (i < code.length && /[\d.]/.test(code[i])) {
                i++;
            }
            tokens.push({ type: 'number', value: code.slice(start, i) });
            continue;
        }
        // 处理标识符（可能是关键字或函数）
        if (/[a-zA-Z_$]/.test(char)) {
            const start = i;
            while (i < code.length && /[a-zA-Z0-9_$]/.test(code[i])) {
                i++;
            }
            const word = code.slice(start, i);
            // 检查是否是函数调用（后面跟着括号）
            let j = i;
            while (j < code.length && /\s/.test(code[j]))
                j++; // 跳过空白
            if (j < code.length && code[j] === '(') {
                tokens.push({ type: 'function', value: word });
            }
            else if (keywords.has(word)) {
                tokens.push({ type: 'keyword', value: word });
            }
            else {
                tokens.push({ type: 'text', value: word });
            }
            continue;
        }
        // 处理其他字符
        tokens.push({ type: 'text', value: char });
        i++;
    }
    return tokens;
}
/**
 * 改进的Markdown渲染器
 */
function renderMarkdown(content) {
    console.log('[Window] [ECS Extension] 开始渲染Markdown，内容长度:', content.length);
    // 检查内容是否为空或异常
    if (!content || content.length === 0) {
        console.warn('[Window] [ECS Extension] 内容为空');
        return '<p>文档内容为空</p>';
    }
    // 检查是否包含异常字符（可能的编码问题）
    if (content.includes('�') || content.includes('\uFFFD')) {
        console.warn('[Window] [ECS Extension] 检测到可能的编码问题');
    }
    // 预处理：转换换行符为统一格式
    let html = content.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
    // 处理代码块（三个反引号）- 必须在其他处理之前
    html = html.replace(/```(\w*)\n?([\s\S]*?)```/g, (match, lang, code) => {
        const language = lang || 'typescript'; // 默认为TypeScript
        const cleanCode = code.trim(); // 不在这里转义，由语法高亮函数处理
        // 先对每行应用语法高亮，再添加行号
        const lines = cleanCode.split('\n');
        const numberedLines = lines.map((line, index) => {
            const highlightedLine = applyTypescriptSyntaxHighlighting(line || ' ');
            // 使用字符串拼接而不是模板字符串，避免与代码中的模板字符串冲突
            return '<div class="code-line"><span class="line-number">' + (index + 1) + '</span><span class="line-content">' + highlightedLine + '</span></div>';
        }).join('');
        // 使用字符串拼接构建HTML，避免模板字符串冲突
        return '<div class="code-block-container">' +
            '<div class="code-header">' +
            '<span class="language-label">' + language.toUpperCase() + '</span>' +
            '<button class="copy-button" onclick="copyCodeToClipboard(this)">📋 复制全部</button>' +
            '</div>' +
            '<pre class="code-block ' + language + '" data-raw-code="' + escapeHtml(cleanCode) + '"><code class="language-' + language + '">' + numberedLines + '</code></pre>' +
            '</div>';
    });
    // 处理行内代码（单个反引号）
    html = html.replace(/`([^`\n]+)`/g, '<code class="inline-code">$1</code>');
    // 处理标题（按从长到短的顺序，避免匹配冲突）
    html = html.replace(/^#### (.+)$/gm, '<h4>$1</h4>');
    html = html.replace(/^### (.+)$/gm, '<h3>$1</h3>');
    html = html.replace(/^## (.+)$/gm, '<h2>$1</h2>');
    html = html.replace(/^# (.+)$/gm, '<h1>$1</h1>');
    // 处理粗体和斜体
    html = html.replace(/\*\*([^*\n]+)\*\*/g, '<strong>$1</strong>');
    html = html.replace(/\*([^*\n]+)\*/g, '<em>$1</em>');
    // 处理链接格式 [text](url) - 支持内部文档跳转
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, (match, text, url) => {
        // 检查是否是内部文档链接（.md文件）
        if (url.endsWith('.md')) {
            const docId = url.replace('.md', '');
            return `<a href="#" class="internal-link" data-doc="${docId}">${text}</a>`;
        }
        // 外部链接
        return `<a href="${url}" target="_blank" class="external-link">${text}</a>`;
    });
    // 处理无序列表（支持多级缩进）
    html = html.replace(/^(\s*)- (.+)$/gm, (match, indent, content) => {
        const level = Math.floor(indent.length / 2); // 每2个空格一个层级
        return `<li data-level="${level}">${content}</li>`;
    });
    // 将连续的li元素包装在ul中，并处理嵌套
    html = html.replace(/(<li.*?<\/li>\s*)+/g, (match) => {
        // 简单处理：先用ul包装所有li，后续可以优化嵌套结构
        return `<ul>${match}</ul>`;
    });
    // 处理引用
    html = html.replace(/^> (.+)$/gm, '<blockquote>$1</blockquote>');
    // 处理表格
    html = html.replace(/^\|(.+)\|$/gm, (match, content) => {
        const cells = content.split('|').map((cell) => cell.trim());
        return `<tr>${cells.map((cell) => `<td>${cell}</td>`).join('')}</tr>`;
    });
    // 包装表格行
    html = html.replace(/(<tr>.*?<\/tr>\s*)+/g, (match) => {
        const rows = match.match(/<tr>.*?<\/tr>/g) || [];
        if (rows.length === 0)
            return match;
        // 检查是否有分隔行（包含 --- 的行）
        let headerIndex = -1;
        rows.forEach((row, index) => {
            if (row.includes('---')) {
                headerIndex = index;
            }
        });
        if (headerIndex > 0) {
            // 有表头
            const headerRows = rows.slice(0, headerIndex).map(row => row.replace(/<td>/g, '<th>').replace(/<\/td>/g, '</th>'));
            const bodyRows = rows.slice(headerIndex + 1);
            rows.splice(headerIndex, 1); // 移除分隔行
            return `<table class="markdown-table">
                <thead>${headerRows.join('')}</thead>
                <tbody>${bodyRows.join('')}</tbody>
            </table>`;
        }
        else {
            // 无表头
            return `<table class="markdown-table">
                <tbody>${rows.join('')}</tbody>
            </table>`;
        }
    });
    // 重新设计段落处理逻辑
    const lines = html.split('\n');
    const processedLines = [];
    let inParagraph = false;
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        // 空行
        if (!line) {
            if (inParagraph) {
                processedLines.push('</p>');
                inParagraph = false;
            }
            continue;
        }
        // 已经是HTML标签的行，不需要包装
        if (line.match(/^<(h[1-6]|ul|ol|li|blockquote|pre|div|\/)/)) {
            if (inParagraph) {
                processedLines.push('</p>');
                inParagraph = false;
            }
            processedLines.push(line);
            continue;
        }
        // 普通文本行，需要包装在段落中
        if (!inParagraph) {
            processedLines.push('<p>');
            inParagraph = true;
        }
        processedLines.push(line);
        // 检查下一行，如果是空行或HTML标签，结束当前段落
        const nextLine = i + 1 < lines.length ? lines[i + 1].trim() : '';
        if (!nextLine || nextLine.match(/^<(h[1-6]|ul|ol|li|blockquote|pre|div|\/)/)) {
            processedLines.push('</p>');
            inParagraph = false;
        }
        else {
            // 在段落内的换行用<br>
            processedLines.push('<br>');
        }
    }
    // 如果最后还在段落中，关闭段落
    if (inParagraph) {
        processedLines.push('</p>');
    }
    html = processedLines.join('\n');
    // 清理多余的换行和空段落
    html = html.replace(/<p>\s*<\/p>/g, '');
    html = html.replace(/<br>\s*<\/p>/g, '</p>');
    html = html.replace(/<p>\s*<br>/g, '<p>');
    console.log('[Window] [ECS Extension] Markdown渲染完成，HTML长度:', html.length);
    return html;
}
/**
 * 获取过滤后的文档列表
 */
function getFilteredDocs() {
    return DOCS_CONFIG.filter(doc => {
        const matchesCategory = currentCategory === 'all' || doc.category === currentCategory;
        const matchesSearch = !searchQuery ||
            doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            doc.description.toLowerCase().includes(searchQuery.toLowerCase());
        return matchesCategory && matchesSearch;
    });
}
/**
 * 渲染分类导航
 */
function renderCategoryNav(container) {
    const categories = [
        { id: 'all', name: '📚 全部文档' },
        ...Object.entries(DOC_CATEGORIES).map(([id, name]) => ({ id, name }))
    ];
    container.innerHTML = categories.map(cat => '<div class="nav-item ' + (currentCategory === cat.id ? 'active' : '') + '" data-category="' + cat.id + '">' +
        cat.name +
        '</div>').join('');
}
/**
 * 渲染文档列表
 */
function renderDocsList(container) {
    const docs = getFilteredDocs();
    if (docs.length === 0) {
        container.innerHTML = '<div class="no-results">未找到匹配的文档</div>';
        return;
    }
    container.innerHTML = docs.map(doc => '<div class="doc-item ' + ((currentDoc === null || currentDoc === void 0 ? void 0 : currentDoc.id) === doc.id ? 'active' : '') + '" data-doc-id="' + doc.id + '">' +
        '<div class="doc-header">' +
        '<span class="doc-title">' + doc.title + '</span>' +
        (doc.recommended ? '<span class="recommended-badge">⭐</span>' : '') +
        '<span class="difficulty-badge ' + doc.difficulty + '">' + getDifficultyText(doc.difficulty) + '</span>' +
        '</div>' +
        '<div class="doc-description">' + doc.description + '</div>' +
        '<div class="doc-meta">' +
        '<span class="doc-size">' + doc.size + '</span>' +
        '<span class="doc-category">' + DOC_CATEGORIES[doc.category] + '</span>' +
        '</div>' +
        '</div>').join('');
}
/**
 * 显示文档内容
 */
function showDocContent(doc, contentContainer, headerContainer) {
    // 显示文档头部信息
    const githubUrl = 'https://github.com/esengine/ecs-framework/blob/master/docs/' + doc.filename;
    headerContainer.innerHTML =
        '<div class="doc-title-section">' +
            '<h1>' + doc.title + '</h1>' +
            '<div class="doc-meta-info">' +
            '<span class="difficulty-badge ' + doc.difficulty + '">' + getDifficultyText(doc.difficulty) + '</span>' +
            '<span class="doc-size">📄 ' + doc.size + '</span>' +
            '<span class="doc-category">' + DOC_CATEGORIES[doc.category] + '</span>' +
            (doc.recommended ? '<span class="recommended-badge">⭐ 推荐</span>' : '') +
            '<a href="' + githubUrl + '" target="_blank" class="github-link">📖 在GitHub上查看</a>' +
            '</div>' +
            '<p class="doc-description">' + doc.description + '</p>' +
            '<div class="doc-notice">' +
            '<span class="notice-icon">💡</span>' +
            '<span class="notice-text">如果文档显示有问题，请点击上方"在GitHub上查看"链接查看原始文档</span>' +
            '</div>' +
            '</div>';
    headerContainer.style.display = 'block';
    // 加载并显示文档内容
    try {
        const content = readDocContent(doc.filename);
        const html = renderMarkdown(content);
        contentContainer.innerHTML = html;
        contentContainer.style.display = 'block';
        // 设置内部链接点击事件
        setupInternalLinks(contentContainer);
        // 设置代码块的增强交互
        setupCodeBlockInteractions(contentContainer);
    }
    catch (error) {
        contentContainer.innerHTML =
            '<div class="error-message">' +
                '<h2>⚠️ 文档加载失败</h2>' +
                '<p>无法加载文档 "' + doc.title + '"</p>' +
                '<p>错误信息: ' + (error instanceof Error ? error.message : String(error)) + '</p>' +
                '</div>';
        contentContainer.style.display = 'block';
    }
}
/**
 * 获取难度颜色
 */
function getDifficultyColor(difficulty) {
    const colors = {
        'beginner': '#52c41a',
        'intermediate': '#1890ff',
        'advanced': '#f5222d'
    };
    return colors[difficulty] || '#666';
}
/**
 * 获取难度文本
 */
function getDifficultyText(difficulty) {
    const texts = {
        'beginner': '新手',
        'intermediate': '进阶',
        'advanced': '高级'
    };
    return texts[difficulty] || difficulty;
}
/**
 * 设置内部链接点击事件
 */
function setupInternalLinks(container) {
    const internalLinks = container.querySelectorAll('a.internal-link');
    internalLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const docId = link.dataset.doc;
            if (docId) {
                const targetDoc = DOCS_CONFIG.find(d => d.id === docId);
                if (targetDoc) {
                    // 触发文档切换
                    currentDoc = targetDoc;
                    // 这里需要重新渲染，但我们需要访问面板元素
                    console.log('[Window] [ECS Extension] 跳转到文档: ' + targetDoc.title);
                    // 发送自定义事件来处理文档切换
                    window.dispatchEvent(new CustomEvent('switchDoc', { detail: targetDoc }));
                }
            }
        });
    });
}
/**
 * 复制代码到剪贴板
 */
function copyCodeToClipboard(button) {
    const codeContainer = button.closest('.code-block-container');
    const preElement = codeContainer === null || codeContainer === void 0 ? void 0 : codeContainer.querySelector('pre');
    if (preElement) {
        // 优先使用原始代码数据
        const rawCode = preElement.dataset.rawCode;
        let textToCopy = '';
        if (rawCode) {
            // 解码HTML实体
            const textarea = document.createElement('textarea');
            textarea.innerHTML = rawCode;
            textToCopy = textarea.value;
        }
        else {
            // 备用方案：提取显示的文本内容（去除行号）
            const lines = preElement.querySelectorAll('.line-content');
            textToCopy = Array.from(lines).map(line => line.textContent).join('\n');
        }
        // 复制到剪贴板
        navigator.clipboard.writeText(textToCopy).then(() => {
            // 显示复制成功提示
            const originalText = button.textContent;
            button.textContent = '✅ 已复制全部';
            button.style.color = '#52c41a';
            setTimeout(() => {
                button.textContent = originalText;
                button.style.color = '';
            }, 2000);
        }).catch(err => {
            console.error('复制失败:', err);
            button.textContent = '❌ 复制失败';
            setTimeout(() => {
                button.textContent = '📋 复制全部';
            }, 2000);
        });
    }
}
/**
 * 设置代码块的增强交互
 */
function setupCodeBlockInteractions(container) {
    const codeBlocks = container.querySelectorAll('.code-block-container');
    codeBlocks.forEach(codeBlock => {
        const preElement = codeBlock.querySelector('pre');
        if (!preElement)
            return;
        // 添加双击选择整行功能
        const lines = codeBlock.querySelectorAll('.code-line');
        lines.forEach(line => {
            line.addEventListener('dblclick', () => {
                const lineContent = line.querySelector('.line-content');
                if (lineContent) {
                    // 选择整行内容
                    const range = document.createRange();
                    range.selectNodeContents(lineContent);
                    const selection = window.getSelection();
                    if (selection) {
                        selection.removeAllRanges();
                        selection.addRange(range);
                    }
                }
            });
        });
        // 添加键盘快捷键支持（Ctrl+A 选择全部代码）
        preElement.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'a') {
                e.preventDefault();
                const codeElement = preElement.querySelector('code');
                if (codeElement) {
                    const range = document.createRange();
                    range.selectNodeContents(codeElement);
                    const selection = window.getSelection();
                    if (selection) {
                        selection.removeAllRanges();
                        selection.addRange(range);
                    }
                }
            }
        });
        // 确保代码块可以获得焦点以响应键盘事件
        preElement.setAttribute('tabindex', '0');
    });
}
// 将函数暴露到全局作用域，供HTML onclick使用
window.copyCodeToClipboard = copyCodeToClipboard;
/**
 * 面板定义
 */
exports.template = (0, fs_extra_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/template/docs/index.html'), { encoding: 'utf8' });
exports.style = (0, fs_extra_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/style/docs/index.css'), { encoding: 'utf8' });
exports.$ = {
    app: '.docs-panel'
};
/**
 * 面板方法
 */
exports.methods = {};
/**
 * 面板就绪
 */
exports.ready = function () {
    console.log('[Window] [ECS Extension] 文档面板准备就绪');
    // 延迟初始化，确保DOM完全加载
    setTimeout(() => {
        try {
            initializeDocPanel(this);
        }
        catch (error) {
            console.error('[Window] [ECS Extension] 文档面板初始化失败:', error);
        }
    }, 200);
};
/**
 * 初始化文档面板
 */
function initializeDocPanel(panel) {
    console.log('[Window] [ECS Extension] 开始初始化文档面板');
    console.log('[Window] [ECS Extension] 面板对象:', panel);
    console.log('[Window] [ECS Extension] 面板.$:', panel.$);
    console.log('[Window] [ECS Extension] 面板.$.app:', panel.$.app);
    // 直接使用面板元素作为根元素
    const rootPanel = panel.$.app;
    if (!rootPanel) {
        console.error('[Window] [ECS Extension] 无法获取面板根元素');
        return;
    }
    console.log('[Window] [ECS Extension] 面板根元素:', rootPanel);
    console.log('[Window] [ECS Extension] 面板根元素类名:', rootPanel.className);
    console.log('[Window] [ECS Extension] 面板根元素HTML:', rootPanel.innerHTML ? rootPanel.innerHTML.substring(0, 500) : '无内容');
    // 获取DOM元素
    const searchInput = rootPanel.querySelector('.search-input');
    const categoryNav = rootPanel.querySelector('.category-nav');
    const docsList = rootPanel.querySelector('.docs-list');
    const docContent = rootPanel.querySelector('.doc-content');
    const docHeaderInfo = rootPanel.querySelector('.doc-header-info');
    const loadingState = rootPanel.querySelector('.loading-state');
    const noDocState = rootPanel.querySelector('.no-doc-state');
    // 详细记录每个元素的查找结果
    console.log('[Window] [ECS Extension] DOM元素查找结果:', {
        searchInput: !!searchInput,
        categoryNav: !!categoryNav,
        docsList: !!docsList,
        docContent: !!docContent,
        docHeaderInfo: !!docHeaderInfo,
        loadingState: !!loadingState,
        noDocState: !!noDocState
    });
    if (!searchInput || !categoryNav || !docsList || !docContent || !docHeaderInfo || !loadingState || !noDocState) {
        console.error('[Window] [ECS Extension] 找不到必要的DOM元素');
        // 尝试直接从根面板查找
        console.log('[Window] [ECS Extension] 尝试从根面板查找元素');
        const altSearchInput = rootPanel.querySelector('.search-input');
        const altCategoryNav = rootPanel.querySelector('.category-nav');
        const altDocsList = rootPanel.querySelector('.docs-list');
        console.log('[Window] [ECS Extension] 从根面板查找结果:', {
            altSearchInput: !!altSearchInput,
            altCategoryNav: !!altCategoryNav,
            altDocsList: !!altDocsList
        });
        if (!altSearchInput || !altCategoryNav || !altDocsList) {
            console.error('[Window] [ECS Extension] 从根面板也找不到必要元素，面板HTML可能有问题');
            return;
        }
        // 使用替代查找的元素
        const altDocContent = rootPanel.querySelector('.doc-content');
        const altDocHeaderInfo = rootPanel.querySelector('.doc-header-info');
        const altLoadingState = rootPanel.querySelector('.loading-state');
        const altNoDocState = rootPanel.querySelector('.no-doc-state');
        setupEventListeners(altSearchInput, altCategoryNav, altDocsList, altDocContent, altDocHeaderInfo, altLoadingState, altNoDocState);
        renderCategoryNav(altCategoryNav);
        renderDocsList(altDocsList);
        return;
    }
    console.log('[Window] [ECS Extension] DOM元素获取成功，开始初始化界面');
    // 初始化界面
    renderCategoryNav(categoryNav);
    renderDocsList(docsList);
    // 设置事件监听器
    setupEventListeners(searchInput, categoryNav, docsList, docContent, docHeaderInfo, loadingState, noDocState);
    // 监听文档切换事件
    window.addEventListener('switchDoc', (e) => {
        const targetDoc = e.detail;
        if (targetDoc) {
            currentDoc = targetDoc;
            renderDocsList(docsList);
            // 隐藏无文档状态，显示加载状态
            noDocState.style.display = 'none';
            loadingState.style.display = 'flex';
            docContent.style.display = 'none';
            docHeaderInfo.style.display = 'none';
            // 延迟显示文档内容
            setTimeout(() => {
                loadingState.style.display = 'none';
                showDocContent(targetDoc, docContent, docHeaderInfo);
            }, 300);
        }
    });
    console.log('[Window] [ECS Extension] 文档面板初始化完成');
}
/**
 * 设置事件监听器
 */
function setupEventListeners(searchInput, categoryNav, docsList, docContent, docHeaderInfo, loadingState, noDocState) {
    // 搜索功能
    if (searchInput) {
        searchInput.addEventListener('input', (e) => {
            searchQuery = e.target.value;
            renderDocsList(docsList);
        });
    }
    // 分类导航
    if (categoryNav) {
        categoryNav.addEventListener('click', (e) => {
            const target = e.target;
            if (target.classList.contains('nav-item')) {
                const category = target.dataset.category;
                if (category) {
                    currentCategory = category;
                    renderCategoryNav(categoryNav);
                    renderDocsList(docsList);
                }
            }
        });
    }
    // 文档列表
    if (docsList) {
        docsList.addEventListener('click', (e) => {
            const target = e.target;
            const docItem = target.closest('.doc-item');
            if (docItem) {
                const docId = docItem.dataset.docId;
                if (docId) {
                    const doc = DOCS_CONFIG.find(d => d.id === docId);
                    if (doc) {
                        currentDoc = doc;
                        renderDocsList(docsList);
                        // 隐藏无文档状态，显示加载状态
                        noDocState.style.display = 'none';
                        loadingState.style.display = 'flex';
                        docContent.style.display = 'none';
                        docHeaderInfo.style.display = 'none';
                        // 延迟显示文档内容（模拟加载）
                        setTimeout(() => {
                            loadingState.style.display = 'none';
                            showDocContent(doc, docContent, docHeaderInfo);
                        }, 300);
                    }
                }
            }
        });
    }
}
/**
 * 面板关闭前
 */
exports.beforeClose = function () {
    console.log('[Window] [ECS Extension] 文档面板即将关闭');
};
/**
 * 面板关闭
 */
exports.close = function () {
    console.log('[Window] [ECS Extension] 文档面板已关闭');
};
//# sourceMappingURL=data:application/json;base64,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