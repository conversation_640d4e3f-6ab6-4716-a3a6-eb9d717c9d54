"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const fs_extra_1 = require("fs-extra");
const path_1 = require("path");
const path = __importStar(require("path"));
const vue_1 = require("vue");
const CodeGenerator_1 = require("../../CodeGenerator");
const panelDataMap = new WeakMap();
module.exports = Editor.Panel.define({
    listeners: {
        show() { },
        hide() { },
    },
    template: `<div id="app"></div>`,
    style: (0, fs_extra_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/style/generator/index.css'), 'utf-8'),
    $: {
        app: '#app',
    },
    ready() {
        if (this.$.app) {
            const app = (0, vue_1.createApp)((0, vue_1.defineComponent)({
                setup() {
                    const featureName = (0, vue_1.ref)('');
                    const options = (0, vue_1.reactive)({
                        generateComponent: true,
                        generateSystem: false
                    });
                    // 组件选项
                    const componentOptions = (0, vue_1.reactive)({
                        includeComments: true,
                        addProperties: []
                    });
                    // 系统选项
                    const systemOptions = (0, vue_1.reactive)({
                        systemType: 'EntitySystem',
                        includeComments: true,
                        requiredComponents: [],
                        filterByComponent: true
                    });
                    // 系统类型定义
                    const systemTypes = [
                        {
                            value: 'EntitySystem',
                            name: 'EntitySystem',
                            icon: '🔄',
                            description: '批量处理实体，适合需要遍历多个实体的逻辑',
                            usage: '适用场景：移动系统、渲染系统、物理碰撞系统'
                        },
                        {
                            value: 'ProcessingSystem',
                            name: 'ProcessingSystem',
                            icon: '⚡',
                            description: '执行全局逻辑，不依赖特定实体',
                            usage: '适用场景：输入处理、音效管理、场景切换'
                        },
                        {
                            value: 'IntervalSystem',
                            name: 'IntervalSystem',
                            icon: '⏰',
                            description: '按时间间隔执行，可控制执行频率',
                            usage: '适用场景：AI决策、状态保存、定时清理'
                        },
                        {
                            value: 'PassiveSystem',
                            name: 'PassiveSystem',
                            icon: '🎯',
                            description: '被动响应，需要手动调用或事件触发',
                            usage: '适用场景：技能释放、道具使用、特殊效果'
                        }
                    ];
                    const isGenerating = (0, vue_1.ref)(false);
                    const previewCode = (0, vue_1.ref)('');
                    const showPreview = (0, vue_1.ref)(false);
                    // 选择系统类型
                    const selectSystemType = (type) => {
                        systemOptions.systemType = type;
                        updatePreview();
                    };
                    // 生成代码
                    const generateCode = async () => {
                        if (!featureName.value.trim()) {
                            Editor.Dialog.warn('请输入功能名称', {
                                detail: '请先输入一个有效的功能名称，例如：Health、Movement、Combat等'
                            });
                            return;
                        }
                        if (!options.generateComponent && !options.generateSystem) {
                            Editor.Dialog.warn('请选择生成内容', {
                                detail: '请至少选择一种要生成的代码类型（组件或系统）'
                            });
                            return;
                        }
                        isGenerating.value = true;
                        try {
                            const projectPath = Editor.Project.path;
                            const ecsDir = path.join(projectPath, 'assets', 'scripts', 'ecs');
                            // 检查ECS目录是否存在
                            const fs = require('fs');
                            if (!fs.existsSync(ecsDir)) {
                                Editor.Dialog.warn('ECS目录不存在', {
                                    detail: '请先创建ECS模板后再生成代码。\n\n您可以在欢迎面板中点击"创建ECS模板"来创建基础结构。',
                                });
                                return;
                            }
                            const codeGenerator = new CodeGenerator_1.CodeGenerator();
                            const generatedFiles = [];
                            const baseName = featureName.value.trim();
                            // 生成组件
                            if (options.generateComponent) {
                                const componentDir = path.join(ecsDir, 'components');
                                await codeGenerator.generateComponent(baseName, componentDir, componentOptions);
                                generatedFiles.push(`📦 组件: ${baseName}Component.ts`);
                            }
                            // 生成系统
                            if (options.generateSystem) {
                                const systemDir = path.join(ecsDir, 'systems');
                                // 如果选择了组件过滤且生成了组件，自动添加组件过滤
                                const requiredComponents = (systemOptions.filterByComponent && options.generateComponent) ?
                                    [`${baseName}Component`] : [];
                                const systemOpts = Object.assign(Object.assign({}, systemOptions), { requiredComponents });
                                await codeGenerator.generateSystem(baseName, systemDir, systemOpts);
                                generatedFiles.push(`⚙️ 系统: ${baseName}System.ts`);
                            }
                            // 成功提示
                            Editor.Dialog.info('代码生成成功', {
                                detail: `✅ ${baseName} 功能代码已生成完成！\n\n生成的文件：\n${generatedFiles.join('\n')}\n\n请刷新资源管理器查看新创建的文件。`
                            });
                            // 清空输入
                            featureName.value = '';
                        }
                        catch (error) {
                            console.error('Failed to generate code:', error);
                            Editor.Dialog.error('代码生成失败', {
                                detail: `生成代码时发生错误：\n\n${error}`
                            });
                        }
                        finally {
                            isGenerating.value = false;
                        }
                    };
                    // 预览代码
                    const previewGeneration = () => {
                        if (!featureName.value.trim()) {
                            showPreview.value = false;
                            return;
                        }
                        const baseName = featureName.value.trim();
                        let preview = `将要生成的文件：\n\n`;
                        if (options.generateComponent) {
                            preview += `📦 组件: ${baseName}Component.ts\n`;
                            preview += `   - 位置: assets/scripts/ecs/components/\n`;
                            preview += `   - 基础组件模板\n\n`;
                        }
                        if (options.generateSystem) {
                            const selectedType = systemTypes.find(t => t.value === systemOptions.systemType);
                            preview += `⚙️ 系统: ${baseName}System.ts\n`;
                            preview += `   - 位置: assets/scripts/ecs/systems/\n`;
                            preview += `   - 类型: ${(selectedType === null || selectedType === void 0 ? void 0 : selectedType.name) || systemOptions.systemType}\n`;
                            if (systemOptions.filterByComponent && options.generateComponent) {
                                preview += `   - 过滤组件: ${baseName}Component\n`;
                            }
                            else if (systemOptions.filterByComponent) {
                                preview += `   - 组件过滤: 需要手动配置\n`;
                            }
                            else {
                                preview += `   - 组件过滤: 无\n`;
                            }
                            preview += `\n`;
                        }
                        previewCode.value = preview;
                        showPreview.value = true;
                    };
                    // 监听功能名称变化
                    const updatePreview = () => {
                        if (featureName.value.trim()) {
                            previewGeneration();
                        }
                        else {
                            showPreview.value = false;
                        }
                    };
                    return {
                        featureName,
                        options,
                        componentOptions,
                        systemOptions,
                        systemTypes,
                        isGenerating,
                        previewCode,
                        showPreview,
                        generateCode,
                        updatePreview,
                        selectSystemType
                    };
                },
                template: (0, fs_extra_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/template/generator/index.html'), 'utf-8')
            }));
            app.config.compilerOptions.isCustomElement = (tag) => tag.startsWith('ui-');
            app.mount(this.$.app);
            panelDataMap.set(this, app);
        }
    },
    beforeClose() { },
    close() {
        const app = panelDataMap.get(this);
        if (app) {
            app.unmount();
            panelDataMap.delete(this);
        }
    },
});
//# sourceMappingURL=data:application/json;base64,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