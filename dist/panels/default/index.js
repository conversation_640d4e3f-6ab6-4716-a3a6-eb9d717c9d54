"use strict";
/* eslint-disable vue/one-component-per-file */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const fs_extra_1 = require("fs-extra");
const path_1 = require("path");
const vue_1 = require("vue");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const child_process_1 = require("child_process");
const panelDataMap = new WeakMap();
/**
 * 检测ECS框架安装状态的工具函数
 */
async function checkEcsFrameworkStatus(projectPath) {
    const packageJsonPath = path.join(projectPath, 'package.json');
    const nodeModulesPath = path.join(projectPath, 'node_modules', '@esengine', 'ecs-framework');
    try {
        // 检查package.json是否存在
        const packageJsonExists = fs.existsSync(packageJsonPath);
        if (!packageJsonExists) {
            return {
                packageJsonExists: false,
                ecsInstalled: false,
                ecsVersion: null
            };
        }
        // 读取package.json
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
        // 检查依赖中是否包含ECS框架
        const dependencies = Object.assign(Object.assign({}, packageJson.dependencies), packageJson.devDependencies);
        const ecsInDeps = dependencies['@esengine/ecs-framework'];
        // 检查node_modules中是否实际安装了ECS框架
        const ecsActuallyInstalled = fs.existsSync(nodeModulesPath);
        let ecsVersion = null;
        if (ecsActuallyInstalled) {
            try {
                const ecsPackageJson = JSON.parse(fs.readFileSync(path.join(nodeModulesPath, 'package.json'), 'utf-8'));
                ecsVersion = ecsPackageJson.version;
            }
            catch (e) {
                console.warn('Unable to read ECS framework version:', e);
            }
        }
        return {
            packageJsonExists: true,
            ecsInstalled: ecsActuallyInstalled && !!ecsInDeps,
            ecsVersion,
            declaredVersion: ecsInDeps
        };
    }
    catch (error) {
        console.error('Error checking ECS framework status:', error);
        return {
            packageJsonExists: false,
            ecsInstalled: false,
            ecsVersion: null
        };
    }
}
/**
 * 检测ECS模板状态
 */
function checkEcsTemplateStatus(projectPath) {
    const ecsDir = path.join(projectPath, 'assets', 'scripts', 'ecs');
    try {
        if (!fs.existsSync(ecsDir)) {
            return {
                templateExists: false,
                existingFiles: []
            };
        }
        // 扫描ECS目录中的文件
        const existingFiles = [];
        function scanDirectory(dirPath, relativePath = '') {
            if (!fs.existsSync(dirPath))
                return;
            const items = fs.readdirSync(dirPath);
            for (const item of items) {
                const fullPath = path.join(dirPath, item);
                const relativeFilePath = relativePath ? `${relativePath}/${item}` : item;
                if (fs.statSync(fullPath).isDirectory()) {
                    scanDirectory(fullPath, relativeFilePath);
                }
                else {
                    existingFiles.push(relativeFilePath);
                }
            }
        }
        scanDirectory(ecsDir);
        return {
            templateExists: existingFiles.length > 0,
            existingFiles
        };
    }
    catch (error) {
        console.error('Error checking ECS template status:', error);
        return {
            templateExists: false,
            existingFiles: []
        };
    }
}
/**
 * 获取ECS框架的最新版本
 */
function getLatestEcsVersion() {
    return new Promise((resolve) => {
        (0, child_process_1.exec)('npm view @esengine/ecs-framework version', (error, stdout) => {
            if (error) {
                console.warn('Failed to get latest version:', error);
                resolve(null);
            }
            else {
                resolve(stdout.trim());
            }
        });
    });
}
/**
 * 获取Node.js版本
 */
function getNodeVersion() {
    return new Promise((resolve) => {
        (0, child_process_1.exec)('node --version', (error, stdout) => {
            if (error) {
                resolve('未知');
            }
            else {
                resolve(stdout.trim().replace('v', ''));
            }
        });
    });
}
/**
 * 比较版本号
 */
function compareVersions(current, latest) {
    try {
        const currentParts = current.split('.').map(Number);
        const latestParts = latest.split('.').map(Number);
        for (let i = 0; i < Math.max(currentParts.length, latestParts.length); i++) {
            const currentPart = currentParts[i] || 0;
            const latestPart = latestParts[i] || 0;
            if (latestPart > currentPart) {
                return true; // 有更新
            }
            else if (latestPart < currentPart) {
                return false; // 当前版本更新
            }
        }
        return false; // 版本相同
    }
    catch (error) {
        console.warn('Version comparison failed:', error);
        return false;
    }
}
/**
 * @zh 如果希望兼容 3.3 之前的版本可以使用下方的代码
 * @en You can add the code below if you want compatibility with versions prior to 3.3
 */
// Editor.Panel.define = Editor.Panel.define || function(options: any) { return options }
module.exports = Editor.Panel.define({
    listeners: {
        show() { console.log('ECS Welcome Panel Show'); },
        hide() { console.log('ECS Welcome Panel Hide'); },
    },
    template: (0, fs_extra_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/template/default/index.html'), 'utf-8'),
    style: (0, fs_extra_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/style/default/index.css'), 'utf-8'),
    $: {
        app: '#app',
    },
    methods: {
        /**
         * 向主进程发送消息的方法
         */
        sendToMain(message, ...args) {
            Editor.Message.send('cocos-ecs-extension', message, ...args);
        }
    },
    ready() {
        if (this.$.app) {
            const app = (0, vue_1.createApp)({});
            app.config.compilerOptions.isCustomElement = (tag) => tag.startsWith('ui-');
            // ECS欢迎组件
            app.component('EcsWelcome', (0, vue_1.defineComponent)({
                setup() {
                    // 响应式状态
                    const checkingStatus = (0, vue_1.ref)(true);
                    const ecsInstalled = (0, vue_1.ref)(false);
                    const ecsVersion = (0, vue_1.ref)(null);
                    const latestVersion = (0, vue_1.ref)(null);
                    const hasUpdate = (0, vue_1.ref)(false);
                    const packageJsonExists = (0, vue_1.ref)(false);
                    const nodeVersion = (0, vue_1.ref)('检测中...');
                    const pluginVersion = (0, vue_1.ref)('1.0.0');
                    const lastCheckTime = (0, vue_1.ref)(null);
                    // ECS模板状态
                    const templateExists = (0, vue_1.ref)(false);
                    const existingFiles = (0, vue_1.ref)([]);
                    // 操作状态
                    const installing = (0, vue_1.ref)(false);
                    const updating = (0, vue_1.ref)(false);
                    const uninstalling = (0, vue_1.ref)(false);
                    // 操作状态显示
                    const showOperationStatus = (0, vue_1.ref)(false);
                    const operationStatusType = (0, vue_1.ref)('loading');
                    const operationStatusMessage = (0, vue_1.ref)('');
                    const operationStatusDetails = (0, vue_1.ref)('');
                    // 显示操作状态
                    const setOperationStatus = (type, message, details) => {
                        showOperationStatus.value = true;
                        operationStatusType.value = type;
                        operationStatusMessage.value = message;
                        operationStatusDetails.value = details || '';
                        // 自动隐藏成功和警告消息
                        if (type === 'success' || type === 'warning') {
                            setTimeout(() => {
                                showOperationStatus.value = false;
                            }, 5000);
                        }
                    };
                    // 获取状态图标
                    const getStatusIcon = (type) => {
                        switch (type) {
                            case 'loading': return '⏳';
                            case 'success': return '✅';
                            case 'error':
                            case 'failed': return '❌';
                            case 'warning': return '⚠️';
                            default: return 'ℹ️';
                        }
                    };
                    // 监听来自主进程的消息 - 暂时注释掉，使用定时刷新
                    const setupMessageListeners = () => {
                        // TODO: 使用正确的消息监听API
                        console.log('Message listeners setup - using polling instead');
                    };
                    // 定时检查状态（用于检测操作完成）
                    let statusCheckInterval = null;
                    const startStatusPolling = () => {
                        if (statusCheckInterval)
                            clearInterval(statusCheckInterval);
                        statusCheckInterval = setInterval(() => {
                            if (installing.value || updating.value || uninstalling.value) {
                                checkStatus();
                            }
                        }, 3000); // 每3秒检查一次
                    };
                    const stopStatusPolling = () => {
                        if (statusCheckInterval) {
                            clearInterval(statusCheckInterval);
                            statusCheckInterval = null;
                        }
                    };
                    // 检测状态的方法
                    const checkStatus = async () => {
                        checkingStatus.value = true;
                        try {
                            // 获取当前项目路径
                            const projectPath = Editor.Project.path;
                            // 检测ECS框架状态
                            const status = await checkEcsFrameworkStatus(projectPath);
                            const prevInstalled = ecsInstalled.value;
                            const prevVersion = ecsVersion.value;
                            packageJsonExists.value = status.packageJsonExists;
                            ecsInstalled.value = status.ecsInstalled;
                            ecsVersion.value = status.ecsVersion;
                            // 检测ECS模板状态
                            const templateStatus = checkEcsTemplateStatus(projectPath);
                            templateExists.value = templateStatus.templateExists;
                            existingFiles.value = templateStatus.existingFiles;
                            // 检测操作完成
                            if (installing.value) {
                                if (status.ecsInstalled && !prevInstalled) {
                                    installing.value = false;
                                    setOperationStatus('success', 'ECS Framework 安装成功！');
                                    stopStatusPolling();
                                }
                                else if (!status.ecsInstalled) {
                                    // 可能还在安装中，继续等待
                                }
                            }
                            if (updating.value) {
                                if (status.ecsVersion && status.ecsVersion !== prevVersion) {
                                    updating.value = false;
                                    setOperationStatus('success', `ECS Framework 更新成功到 v${status.ecsVersion}！`);
                                    stopStatusPolling();
                                }
                            }
                            if (uninstalling.value) {
                                if (!status.ecsInstalled && prevInstalled) {
                                    uninstalling.value = false;
                                    setOperationStatus('success', 'ECS Framework 卸载成功！');
                                    stopStatusPolling();
                                }
                            }
                            // 获取Node.js版本
                            nodeVersion.value = await getNodeVersion();
                            // 检查更新
                            if (ecsInstalled.value && ecsVersion.value) {
                                await checkForUpdates();
                            }
                            // 更新检查时间
                            lastCheckTime.value = new Date().toLocaleString();
                        }
                        catch (error) {
                            console.error('Status check failed:', error);
                            // 如果检查失败，停止操作状态
                            if (installing.value || updating.value || uninstalling.value) {
                                installing.value = false;
                                updating.value = false;
                                uninstalling.value = false;
                                setOperationStatus('error', '状态检查失败，请手动验证操作结果');
                                stopStatusPolling();
                            }
                        }
                        finally {
                            checkingStatus.value = false;
                        }
                    };
                    // 检查更新
                    const checkForUpdates = async () => {
                        if (!ecsInstalled.value || !ecsVersion.value) {
                            setOperationStatus('warning', '请先安装 ECS Framework');
                            return;
                        }
                        try {
                            setOperationStatus('loading', '正在检查更新...');
                            const latest = await getLatestEcsVersion();
                            if (latest) {
                                latestVersion.value = latest;
                                const needsUpdate = compareVersions(ecsVersion.value, latest);
                                hasUpdate.value = needsUpdate;
                                if (needsUpdate) {
                                    setOperationStatus('success', `发现新版本：v${latest}（当前：v${ecsVersion.value}）`);
                                }
                                else {
                                    setOperationStatus('success', `已是最新版本：v${ecsVersion.value}`);
                                }
                            }
                            else {
                                setOperationStatus('warning', '无法获取最新版本信息，请检查网络连接');
                            }
                            // 更新检查时间
                            lastCheckTime.value = new Date().toLocaleString();
                        }
                        catch (error) {
                            console.warn('Failed to check updates:', error);
                            setOperationStatus('error', '检查更新失败，请检查网络连接');
                        }
                    };
                    // 操作方法
                    const installEcsFramework = () => {
                        if (!packageJsonExists.value || installing.value)
                            return;
                        Editor.Dialog.info('安装 ECS Framework', {
                            detail: '即将安装@esengine/ecs-framework到当前项目...',
                            buttons: ['确定', '取消'],
                            default: 0,
                        }).then((result) => {
                            if (result.response === 0) {
                                installing.value = true;
                                setOperationStatus('loading', '正在安装 ECS Framework...');
                                startStatusPolling();
                                // 发送安装命令到主进程
                                Editor.Message.send('cocos-ecs-extension', 'install-ecs-framework');
                            }
                        });
                    };
                    const updateEcsFramework = () => {
                        if (!hasUpdate.value || updating.value)
                            return;
                        Editor.Dialog.info('更新 ECS Framework', {
                            detail: `即将更新ECS框架从 v${ecsVersion.value} 到 v${latestVersion.value}`,
                            buttons: ['确定', '取消'],
                            default: 0,
                        }).then((result) => {
                            if (result.response === 0) {
                                updating.value = true;
                                setOperationStatus('loading', `正在更新 ECS Framework 到 v${latestVersion.value}...`);
                                startStatusPolling();
                                Editor.Message.send('cocos-ecs-extension', 'update-ecs-framework', latestVersion.value);
                            }
                        });
                    };
                    const uninstallEcsFramework = () => {
                        if (uninstalling.value)
                            return;
                        Editor.Dialog.warn('卸载 ECS Framework', {
                            detail: '确定要卸载ECS框架吗？这将删除项目中的ECS框架依赖。',
                            buttons: ['确定卸载', '取消'],
                            default: 1,
                        }).then((result) => {
                            if (result.response === 0) {
                                uninstalling.value = true;
                                setOperationStatus('loading', '正在卸载 ECS Framework...');
                                startStatusPolling();
                                Editor.Message.send('cocos-ecs-extension', 'uninstall-ecs-framework');
                            }
                        });
                    };
                    const openDocsPanel = () => {
                        Editor.Message.send('cocos-ecs-extension', 'open-docs');
                    };
                    const openDocumentation = () => {
                        if (!ecsInstalled.value)
                            return;
                        Editor.Message.send('cocos-ecs-extension', 'open-documentation');
                    };
                    const createEcsTemplate = () => {
                        if (!ecsInstalled.value || templateExists.value)
                            return;
                        Editor.Dialog.info('创建 ECS 模板', {
                            detail: '即将创建基础的ECS项目结构和启动代码...',
                            buttons: ['确定', '取消'],
                            default: 0,
                        }).then((result) => {
                            if (result.response === 0) {
                                Editor.Message.send('cocos-ecs-extension', 'create-ecs-template');
                            }
                        });
                    };
                    const openGithub = () => {
                        Editor.Message.send('cocos-ecs-extension', 'open-github');
                    };
                    const joinQQGroup = () => {
                        Editor.Message.send('cocos-ecs-extension', 'open-qq-group');
                    };
                    const openGenerator = () => {
                        Editor.Message.send('cocos-ecs-extension', 'open-generator');
                    };
                    // 组件挂载后检测状态
                    (0, vue_1.onMounted)(() => {
                        setupMessageListeners();
                        checkStatus();
                    });
                    return {
                        checkingStatus,
                        ecsInstalled,
                        ecsVersion,
                        latestVersion,
                        hasUpdate,
                        packageJsonExists,
                        nodeVersion,
                        pluginVersion,
                        lastCheckTime,
                        templateExists,
                        existingFiles,
                        installing,
                        updating,
                        uninstalling,
                        showOperationStatus,
                        operationStatusType,
                        operationStatusMessage,
                        operationStatusDetails,
                        getStatusIcon,
                        installEcsFramework,
                        updateEcsFramework,
                        uninstallEcsFramework,
                        checkForUpdates,
                        openDocsPanel,
                        openDocumentation,
                        createEcsTemplate,
                        openGithub,
                        joinQQGroup,
                        openGenerator
                    };
                },
                template: (0, fs_extra_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/template/vue/welcome.html'), 'utf-8'),
            }));
            app.mount(this.$.app);
            panelDataMap.set(this, app);
        }
    },
    beforeClose() { },
    close() {
        const app = panelDataMap.get(this);
        if (app) {
            app.unmount();
        }
    },
});
//# sourceMappingURL=data:application/json;base64,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