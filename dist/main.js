"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.methods = void 0;
exports.load = load;
exports.unload = unload;
const handlers_1 = require("./handlers");
/**
 * @en Registration method for the main process of Extension
 * @zh 为扩展的主进程的注册方法
 */
exports.methods = {
    // ================ 面板管理 ================
    /**
     * 打开默认面板
     */
    openPanel() {
        handlers_1.PanelHandler.openDefaultPanel();
    },
    /**
     * 打开调试面板
     */
    'open-debug'() {
        handlers_1.PanelHandler.openDebugPanel();
    },
    /**
     * 打开代码生成器面板
     */
    'open-generator'() {
        handlers_1.PanelHandler.openGeneratorPanel();
    },
    /**
     * 打开文档面板
     */
    'open-docs'() {
        handlers_1.PanelHandler.openDocsPanel();
    },
    // ================ ECS框架管理 ================
    /**
     * 安装ECS Framework
     */
    'install-ecs-framework'() {
        handlers_1.EcsFrameworkHandler.install();
    },
    /**
     * 更新ECS Framework
     */
    'update-ecs-framework'() {
        handlers_1.EcsFrameworkHandler.update();
    },
    /**
     * 卸载ECS Framework
     */
    'uninstall-ecs-framework'() {
        handlers_1.EcsFrameworkHandler.uninstall();
    },
    /**
     * 打开文档
     */
    'open-documentation'() {
        handlers_1.EcsFrameworkHandler.openDocumentation();
    },
    /**
     * 创建ECS模板
     */
    'create-ecs-template'() {
        handlers_1.EcsFrameworkHandler.createTemplate();
    },
    /**
     * 打开GitHub仓库
     */
    'open-github'() {
        handlers_1.EcsFrameworkHandler.openGitHub();
    },
    /**
     * 打开QQ群
     */
    'open-qq-group'() {
        handlers_1.EcsFrameworkHandler.openQQGroup();
    },
    // ================ 热更新管理 ================
    /**
     * 检查插件更新
     */
    'check-plugin-updates'() {
        // 功能暂时禁用，显示提示信息
        Editor.Dialog.info('检查更新功能暂不可用', {
            detail: '热更新功能正在开发中，敬请期待后续版本更新。'
        });
        return { success: false, message: '功能暂不可用' };
    },
    /**
     * 设置热更新配置
     */
    'set-hot-update-config'(...args) {
        const config = args.length >= 2 ? args[1] : args[0];
        return handlers_1.HotUpdateHandler.setConfig(config);
    },
    /**
     * 获取热更新配置
     */
    'get-hot-update-config'() {
        return handlers_1.HotUpdateHandler.getConfig();
    },
    // ================ 资源菜单相关 ================
    /**
     * 创建ECS组件
     */
    'create-ecs-component'(assetInfo) {
        return handlers_1.EcsFrameworkHandler.createComponent(assetInfo);
    },
    /**
     * 创建ECS系统
     */
    'create-ecs-system'(assetInfo) {
        return handlers_1.EcsFrameworkHandler.createSystem(assetInfo);
    },
    /**
     * 重新生成模板代码
     */
    'regenerate-template'(assetInfo) {
        return handlers_1.EcsFrameworkHandler.regenerateTemplate(assetInfo);
    },
    /**
     * 添加到ECS管理器
     */
    'add-to-manager'(assetInfo) {
        return handlers_1.EcsFrameworkHandler.addToManager(assetInfo);
    },
};
/**
 * @en Method triggered when the extension is started
 * @zh 启动扩展时触发的方法
 */
function load() {
    console.log('[Cocos ECS Extension] 扩展已加载');
    // 初始化热更新系统
    handlers_1.HotUpdateHandler.initialize().catch(error => {
        console.error('[Cocos ECS Extension] 热更新初始化失败:', error);
    });
}
/**
 * @en Method triggered when the extension is uninstalled
 * @zh 卸载扩展时触发的方法
 */
function unload() {
    console.log('[Cocos ECS Extension] 扩展已卸载');
    // 清理热更新资源
    handlers_1.HotUpdateHandler.cleanup();
}
//# sourceMappingURL=data:application/json;base64,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