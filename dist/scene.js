"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.methods = void 0;
exports.load = load;
exports.unload = unload;
const path_1 = require("path");
// 添加编辑器内的模块搜索路径
module.paths.push((0, path_1.join)(Editor.App.path, 'node_modules'));
function load() {
    console.log('ECS Debug Scene Script loaded');
}
function unload() {
    console.log('ECS Debug Scene Script unloaded');
}
exports.methods = {
    /**
     * 获取预览状态
     * @returns {object} 预览状态信息
     */
    getPreviewState() {
        try {
            // 检查是否在游戏运行状态
            const { director } = require('cc');
            if (director && director.getScene && director.getScene()) {
                return {
                    isRunning: true,
                    engineLoaded: true
                };
            }
            return {
                isRunning: false,
                engineLoaded: false
            };
        }
        catch (error) {
            console.warn('Failed to get preview state:', error);
            return {
                isRunning: false,
                engineLoaded: false
            };
        }
    },
    /**
     * 检查ECS框架是否已加载
     * @returns {boolean} ECS框架加载状态
     */
    isECSFrameworkLoaded() {
        try {
            // 检查是否有ECS框架的全局对象
            return typeof window !== 'undefined' && !!window.ECSFramework;
        }
        catch (error) {
            console.warn('Failed to check ECS framework status:', error);
            return false;
        }
    },
    /**
     * 获取场景基本信息
     * @returns {object} 场景信息
     */
    getSceneBasicInfo() {
        try {
            const { director } = require('cc');
            if (director && director.getScene) {
                const scene = director.getScene();
                return {
                    sceneName: scene ? (scene.name || '当前场景') : '未知场景',
                    nodeCount: scene ? this.countNodes(scene) : 0,
                    isValid: scene ? scene.isValid : false
                };
            }
            return {
                sceneName: '未知场景',
                nodeCount: 0,
                isValid: false
            };
        }
        catch (error) {
            console.warn('Failed to get scene basic info:', error);
            return {
                sceneName: '获取失败',
                nodeCount: 0,
                isValid: false
            };
        }
    },
    /**
     * 获取ECS框架的调试信息
     * @returns {object|null} ECS调试数据或null（如果框架未加载）
     */
    getECSDebugInfo() {
        var _a, _b, _c;
        try {
            // 检查是否有ECS框架的全局对象
            if (typeof window !== 'undefined' && window.ECSFramework) {
                const ecs = window.ECSFramework;
                // 获取当前场景和实体管理器
                if (ecs.Core && ecs.Core.getCurrentScene) {
                    const scene = ecs.Core.getCurrentScene();
                    if (scene && scene.entityManager) {
                        const entityManager = scene.entityManager;
                        const systemManager = scene.systemManager;
                        // 收集调试信息
                        const debugInfo = {
                            timestamp: new Date().toISOString(),
                            frameworkLoaded: true,
                            currentScene: scene.name || '当前场景',
                            totalEntities: entityManager.entityCount || 0,
                            activeEntities: entityManager.activeEntityCount || 0,
                            pendingAdd: 0, // 需要具体API
                            pendingRemove: 0, // 需要具体API
                            totalSystems: systemManager ? systemManager.getSystemCount() : 0,
                            systemsInfo: [],
                            frameTime: 0, // 需要性能监控
                            memoryUsage: 0, // 需要内存监控
                            componentTypes: 0, // 需要组件统计
                            componentInstances: 0 // 需要组件实例统计
                        };
                        // 获取系统信息
                        if (systemManager && systemManager.getSystems) {
                            const systems = systemManager.getSystems();
                            debugInfo.systemsInfo = systems.map((system, index) => ({
                                name: system.constructor.name || `System${index}`,
                                entityCount: system.entities ? system.entities.length : 0,
                                executionTime: system.lastExecutionTime || 0,
                                updateOrder: index + 1
                            }));
                        }
                        return debugInfo;
                    }
                }
            }
            // 检查是否直接导入了ECS模块
            try {
                // 这里需要根据实际的ECS框架导入方式调整
                const { Core } = require('ecs-framework');
                if (Core) {
                    const scene = Core.getCurrentScene();
                    if (scene) {
                        return {
                            timestamp: new Date().toISOString(),
                            frameworkLoaded: true,
                            currentScene: scene.name || '当前场景',
                            totalEntities: ((_a = scene.entityManager) === null || _a === void 0 ? void 0 : _a.entityCount) || 0,
                            activeEntities: ((_b = scene.entityManager) === null || _b === void 0 ? void 0 : _b.activeEntityCount) || 0,
                            pendingAdd: 0,
                            pendingRemove: 0,
                            totalSystems: ((_c = scene.systemManager) === null || _c === void 0 ? void 0 : _c.getSystemCount()) || 0,
                            systemsInfo: [],
                            frameTime: 0,
                            memoryUsage: 0,
                            componentTypes: 0,
                            componentInstances: 0
                        };
                    }
                }
            }
            catch (error) {
                // ECS框架未导入或未初始化
            }
            return null;
        }
        catch (error) {
            console.warn('Failed to get ECS debug info:', error);
            return null;
        }
    },
    /**
     * 递归计算节点数量
     * @param {any} node
     * @returns {number}
     */
    countNodes(node) {
        if (!node)
            return 0;
        let count = 1; // 当前节点
        if (node.children) {
            for (const child of node.children) {
                count += this.countNodes(child);
            }
        }
        return count;
    }
};
//# sourceMappingURL=data:application/json;base64,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