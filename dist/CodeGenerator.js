"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeGenerator = void 0;
const fs_extra_1 = require("fs-extra");
const path_1 = require("path");
class CodeGenerator {
    /**
     * 生成组件代码
     */
    async generateComponent(name, targetDir, options = {
        includeComments: true,
        addProperties: []
    }) {
        const className = `${name}Component`;
        const fileName = `${className}.ts`;
        const filePath = (0, path_1.join)(targetDir, fileName);
        await (0, fs_extra_1.ensureDir)(targetDir);
        const comments = options.includeComments ? this.generateComponentComments(className) : '';
        const properties = this.generateComponentProperties(options.addProperties);
        const content = `import { Component } from '@esengine/ecs-framework';

${comments}
export class ${className} extends Component {
${properties}
    
    constructor() {
        super();
    }

    /**
     * 重置组件状态
     */
    public reset(): void {
        // 重置组件属性到默认值
${this.generateResetCode(options.addProperties)}
    }
}
`;
        await (0, fs_extra_1.writeFile)(filePath, content, 'utf-8');
    }
    /**
     * 生成系统代码
     */
    async generateSystem(name, targetDir, options = {
        includeComments: true,
        systemType: 'EntitySystem',
        requiredComponents: []
    }) {
        const className = `${name}System`;
        const fileName = `${className}.ts`;
        const filePath = (0, path_1.join)(targetDir, fileName);
        await (0, fs_extra_1.ensureDir)(targetDir);
        const comments = options.includeComments ? this.generateSystemComments(className, options.systemType) : '';
        const imports = this.getSystemImports(options.systemType, options.requiredComponents);
        const matcherSetup = options.requiredComponents.length > 0 ?
            `Matcher.empty().all(${options.requiredComponents.join(', ')})` :
            `Matcher.empty()`;
        const processMethod = this.generateProcessMethod(options.systemType, options.requiredComponents, className);
        const content = `${imports}

${comments}
export class ${className} extends ${options.systemType} {

    constructor() {
        super(${matcherSetup}${options.systemType === 'IntervalSystem' ? ', 1000 / 60' : ''})${options.systemType === 'IntervalSystem' ? '; // 60fps' : ';'}
    }

${processMethod}

    /**
     * 系统开始时调用
     */
    public begin(): void {
        super.begin();
        // 添加系统初始化逻辑
    }

    /**
     * 系统结束时调用
     */
    public end(): void {
        // 添加系统清理逻辑
        super.end();
    }
}
`;
        await (0, fs_extra_1.writeFile)(filePath, content, 'utf-8');
    }
    // ============ 辅助方法 ============
    generateComponentComments(className) {
        return `/**
 * ${className}
 * 
 * 组件描述
 * 
 * @example
 * \`\`\`typescript
 * const entity = scene.createEntity("Example");
 * const component = entity.addComponent(new ${className}());
 * \`\`\`
 */`;
    }
    generateSystemComments(className, systemType) {
        const descriptions = {
            'EntitySystem': '处理拥有特定组件的实体',
            'ProcessingSystem': '执行全局游戏逻辑',
            'IntervalSystem': '按时间间隔处理实体',
            'PassiveSystem': '被动响应事件或手动调用'
        };
        return `/**
 * ${className}
 * 
 * ${descriptions[systemType] || '处理游戏逻辑'}
 * 
 * @example
 * \`\`\`typescript
 * const system = new ${className}();
 * scene.addEntityProcessor(system);
 * \`\`\`
 */`;
    }
    generateComponentProperties(properties) {
        if (properties.length === 0) {
            return '    // 添加组件属性\n    // public value: number = 0;';
        }
        return properties.map(prop => {
            const [name, type = 'number', defaultValue = '0'] = prop.split(':');
            return `    public ${name}: ${type} = ${defaultValue};`;
        }).join('\n');
    }
    generateResetCode(properties) {
        if (properties.length === 0) {
            return '        // this.value = 0;';
        }
        return properties.map(prop => {
            const [name, , defaultValue = '0'] = prop.split(':');
            return `        this.${name} = ${defaultValue};`;
        }).join('\n');
    }
    getSystemImports(systemType, requiredComponents) {
        const imports = [systemType, 'Entity'];
        // 所有系统类型都可能需要Matcher来过滤组件
        if (requiredComponents.length > 0 || systemType === 'EntitySystem' || systemType === 'IntervalSystem' || systemType === 'PassiveSystem') {
            imports.push('Matcher');
        }
        return `import { ${imports.join(', ')} } from '@esengine/ecs-framework';${requiredComponents.length > 0 ? '\n' + this.generateComponentImports(requiredComponents) : ''}`;
    }
    generateComponentImports(components) {
        return components.map(comp => `import { ${comp} } from '../components/${comp}';`).join('\n');
    }
    generateProcessMethod(systemType, requiredComponents, className) {
        switch (systemType) {
            case 'EntitySystem':
                return `    protected process(entities: Entity[]): void {
        for (const entity of entities) {
            this.processEntity(entity);
        }
    }

    private processEntity(entity: Entity): void {
${this.generateProcessingLogic(requiredComponents)}
    }`;
            case 'ProcessingSystem':
                return `    public processSystem(): void {
        // 添加全局系统逻辑
        console.log('${className} processSystem called');
    }`;
            case 'IntervalSystem':
                return `    protected process(entities: Entity[]): void {
        const intervalDelta = this.getIntervalDelta();
        console.log(\`${className} executing with interval delta: \${intervalDelta}\`);
        
        for (const entity of entities) {
            this.processEntity(entity, intervalDelta);
        }
    }

    private processEntity(entity: Entity, delta: number): void {
${this.generateProcessingLogic(requiredComponents)}
    }`;
            case 'PassiveSystem':
                return `    /**
     * 被动系统不主动处理实体
     * 通常用于响应事件或被其他系统调用
     */
    public processEntity(entity: Entity): void {
${this.generateProcessingLogic(requiredComponents)}
    }

    /**
     * 手动触发处理
     */
    public trigger(): void {
        for (const entity of this.entities) {
            this.processEntity(entity);
        }
    }`;
            default:
                return '';
        }
    }
    generateProcessingLogic(requiredComponents) {
        if (requiredComponents.length === 0) {
            return '        // 添加处理逻辑';
        }
        const componentVars = requiredComponents.map((comp) => {
            const varName = comp.replace('Component', '').toLowerCase();
            return `        const ${varName} = entity.getComponent(${comp});`;
        }).join('\n');
        const nullCheck = requiredComponents.map((comp) => {
            const varName = comp.replace('Component', '').toLowerCase();
            return varName;
        }).join(' && ');
        return `${componentVars}
        
        if (${nullCheck}) {
            // 添加处理逻辑
        }`;
    }
}
exports.CodeGenerator = CodeGenerator;
//# sourceMappingURL=data:application/json;base64,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