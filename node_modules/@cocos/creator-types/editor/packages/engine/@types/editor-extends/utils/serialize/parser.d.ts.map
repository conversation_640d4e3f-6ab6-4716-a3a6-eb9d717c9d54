{"version": 3, "file": "parser.d.ts", "sourceRoot": "", "sources": ["../../../../source/editor-extends/utils/serialize/parser.ts"], "names": [], "mappings": "AA0BA,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,gBAAgB,CAAC;AAO1D,UAAU,gBAAgB;IACtB,oBAAoB,CAAC,EAAE,MAAM,CAAC;IAC9B,YAAY,CAAC,EAAE,GAAG,CAAC;IACnB,YAAY,CAAC,EAAE,MAAM,CAAC;CACzB;AACD,MAAM,MAAM,eAAe,GAAG,gBAAgB,GAAG,IAAI,CAAC;AAEtD,MAAM,WAAW,aAAc,SAAQ,gBAAgB;IAEnD,cAAc,EAAE,GAAG,EAAE,CAAC;CACzB;AAED,MAAM,WAAW,aAAc,SAAQ,gBAAgB;IACnD,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;CAChC;AAED,MAAM,WAAW,mBAAoB,SAAQ,aAAa;IACtD,OAAO,EAAE,GAAG,CAAC;CAChB;AAQD,MAAM,WAAW,eAAe;CAAI;AAGpC,MAAM,WAAW,cAAc;IAE3B,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,oBAAoB,CAAC,EAAE,GAAG,CAAC;IAC3B,qBAAqB,CAAC,EAAE,GAAG,CAAC;IAC5B,gCAAgC,CAAC,EAAE,OAAO,CAAC;IAE3C,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB,YAAY,CAAC,EAAE,OAAO,CAAC;IAGvB,kBAAkB,CAAC,EAAE,MAAM,EAAE,CAAC;CACjC;AA0DD,qBAAa,MAAM;IACf,SAAS,EAAE,OAAO,CAAC;IACnB,iBAAiB,EAAE,OAAO,CAAC;IAC3B,cAAc,EAAE,OAAO,CAAC;IACxB,gBAAgB,EAAE,OAAO,CAAC;IAC1B,oBAAoB,EAAE,GAAG,CAAC;IAC1B,qBAAqB,EAAE,GAAG,CAAC;IAC3B,mCAAmC,EAAE,OAAO,CAAC;IAC7C,YAAY,EAAE,OAAO,CAAC;IACtB,kBAAkB,EAAE,cAAc,CAAC,oBAAoB,CAAC,CAAC;IAEzD,OAAO,CAAC,OAAO,CAAU;IACzB,OAAO,CAAC,IAAI,CAAqB;IACjC,OAAO,CAAC,UAAU,CAAqB;IACvC,OAAO,CAAC,WAAW,CAA0B;IAE7C,OAAO,CAAC,YAAY,CAAsC;IAE1D,OAAO,CAAC,uBAAuB,CAAM;IACrC,OAAO,CAAC,qBAAqB,CAA0B;IACvD,OAAO,CAAC,YAAY,CAAC,CAAc;gBAEvB,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc;IAqCrD,KAAK,CAAC,GAAG,EAAE,MAAM;IAsBjB,OAAO,CAAC,iBAAiB;IAczB,OAAO,CAAC,YAAY;IA0BpB,OAAO,CAAC,YAAY;IAmBpB,OAAO,CAAC,oBAAoB;IAmE5B,OAAO,CAAC,sBAAsB;IAI9B,OAAO,CAAC,cAAc;IAqEtB,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,GAAG,OAAO;IAMtC,OAAO,CAAC,UAAU;IAiClB;;;;;OAKG;IACH,OAAO,CAAC,aAAa;IAmNrB,OAAO,CAAC,aAAa;IAuBrB,OAAO,CAAC,mBAAmB;CA0B9B;AAED,MAAM,WAAW,QAAS,SAAQ,cAAc,EAAE,eAAe;CAAI;AACrE,MAAM,CAAC,OAAO,UAAU,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,IAAI,GAAG,SAAS,CAAC,EAAE,OAAO,EAAE,QAAQ,GAAG,MAAM,GAAG,MAAM,CAkBzG"}