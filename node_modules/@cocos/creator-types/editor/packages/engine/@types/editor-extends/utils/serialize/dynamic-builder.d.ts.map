{"version": 3, "file": "dynamic-builder.d.ts", "sourceRoot": "", "sources": ["../../../../source/editor-extends/utils/serialize/dynamic-builder.ts"], "names": [], "mappings": "AAAA,OAAO,EACH,KAAK,EACL,SAAS,EAIT,WAAW,EACd,MAAM,IAAI,CAAC;AAEZ,OAAO,EACH,eAAe,EACf,aAAa,EACb,aAAa,EACb,mBAAmB,EACnB,eAAe,EAClB,MAAM,UAAU,CAAC;AAOlB,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,gBAAgB,CAAC;AAG1D,kBAAU,MAAM,CAAC;IACb,MAAM,WAAW,KAAK;QAClB,QAAQ,EAAE,MAAM,CAAC;QACjB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC;KAC3B;IACD,MAAM,WAAW,eAAgB,SAAQ,KAAK;QAC1C,OAAO,EAAE,GAAG,CAAC;KAChB;IACD,MAAM,WAAW,UAAW,SAAQ,KAAK;QACrC,QAAQ,EAAE,YAAY,CAAC;QACvB,IAAI,EAAE,MAAM,CAAC;QACb,KAAK,EAAE,GAAG,EAAE,CAAC;KAChB;IACD,MAAM,WAAW,iBAAiB;QAC9B,MAAM,EAAE,MAAM,CAAC;KAClB;IACD,MAAM,WAAW,cAAc;QAC3B,QAAQ,EAAE,MAAM,CAAC;KACpB;IAED,KAAK,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC;IAChD,KAAK,IAAI,GAAG,KAAK,GAAG,iBAAiB,GAAG,OAAO,CAAC;IAChD,KAAK,IAAI,GAAG;SACP,GAAG,IAAI,MAAM,GAAG,OAAO;KAC3B,CAAC;IACF,KAAK,KAAK,GAAG,OAAO,EAAE,CAAC;IAEvB,MAAM,MAAM,MAAM,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;IAC1C,MAAM,MAAM,OAAO,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC;;CAC7C;AAED,UAAU,SAAU,SAAQ,eAAe;IAEvC,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC;IAErB,EAAE,EAAE,MAAM,CAAC;CACd;AAED,MAAM,CAAC,OAAO,OAAO,cAAe,SAAQ,OAAO;IAC/C,WAAW,EAAE,OAAO,CAAC;IAGrB,OAAO,CAAC,cAAc,CAAa;gBAEvB,OAAO,EAAE,eAAe;IAKpC,iBAAiB,CAAC,KAAK,EAAE,MAAM,GAAC,IAAI,EAAE,SAAS,EAAE,SAAS,GAAC,IAAI,EAAE,GAAG,EAAE,MAAM,GAAC,MAAM,EAAE,OAAO,EAAE,aAAa,GAAG,SAAS;IAIvH,gBAAgB,CAAC,KAAK,EAAE,MAAM,GAAC,IAAI,EAAE,SAAS,EAAE,SAAS,GAAC,IAAI,EAAE,GAAG,EAAE,MAAM,GAAC,MAAM,EAAE,OAAO,EAAE,eAAe,GAAG,SAAS;IAIxH,OAAO,CAAC,SAAS;IAoBjB,iBAAiB,CAAC,KAAK,EAAE,MAAM,GAAC,IAAI,EAAE,SAAS,EAAE,SAAS,GAAC,IAAI,EAAE,GAAG,EAAE,MAAM,GAAC,MAAM,EAAE,OAAO,EAAE,aAAa,GAAG,SAAS;IAOvH,2BAA2B,CAAC,KAAK,EAAE,MAAM,GAAC,IAAI,EAAE,SAAS,EAAE,SAAS,GAAC,IAAI,EAAE,GAAG,EAAE,MAAM,GAAG,MAAM,EAAE,OAAO,EAAE,mBAAmB,GAAG,SAAS;IAUzI,wBAAwB,CAAC,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,GAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,oBAAoB,EAAE,MAAM,GAAC,IAAI,GAAG,IAAI;IAgBjI,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,GAAC,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,eAAe,GAAG,IAAI;IAOpH,qBAAqB,CAAC,KAAK,EAAE,MAAM,GAAC,IAAI,EAAE,SAAS,EAAE,SAAS,GAAC,IAAI,EAAE,GAAG,EAAE,MAAM,GAAC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,GAAG,SAAS;IA0B/I,sBAAsB,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,GAAC,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,eAAe,GAAG,IAAI;IAgC3H,qBAAqB,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,GAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,GAAG,IAAI;IAU5H,OAAO,CAAC,OAAO,EAAE,SAAS,GAAG,IAAI;IAIjC,SAAS,CAAC,gBAAgB;CAU7B;AAED;;GAEG;AACH,wBAAgB,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,GAAE,WAAW,CAAC,KAAK,CAAS,GAAG,KAAK,GAAG,IAAI,CAQpF;AAED;;GAEG;AACH,wBAAgB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,QAMzD;AAED,wBAAgB,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM;;SAahE"}