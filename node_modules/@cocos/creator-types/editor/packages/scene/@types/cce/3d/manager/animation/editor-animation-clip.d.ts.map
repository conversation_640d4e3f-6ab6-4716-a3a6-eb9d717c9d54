{"version": 3, "file": "editor-animation-clip.d.ts", "sourceRoot": "", "sources": ["../../../../../source/script/3d/manager/animation/editor-animation-clip.ts"], "names": [], "mappings": "AAIA,OAAO,EACH,oBAAoB,EACpB,oBAAoB,EACpB,oBAAoB,EACpB,oBAAoB,EACpB,mBAAmB,EACnB,mBAAmB,EACnB,qBAAqB,EACrB,qBAAqB,EACrB,gBAAgB,EAEhB,uBAAuB,EAEvB,kBAAkB,EAClB,eAAe,EAClB,MAAM,+BAA+B,CAAC;AACvC,OAAO,EAAE,gBAAgB,EAAE,MAAM,8BAA8B,CAAC;AAEhE,OAAO,EACH,SAAS,EACT,mBAAmB,EACnB,aAAa,EAEb,cAAc,EACd,IAAI,EASP,MAAM,IAAI,CAAC;AAEZ,OAAO,EAAE,oBAAoB,EAAE,MAAM,0BAA0B,CAAC;AAChE,OAAO,4BAA4B,MAAM,mCAAmC,CAAC;AAK7E,OAAO,EACH,kBAAkB,EAClB,UAAU,EAEV,UAAU,EAEV,eAAe,EAEf,aAAa,EAEhB,MAAM,gBAAgB,CAAC;AAExB,OAAO,wBAAwB,MAAM,+BAA+B,CAAC;AAmBrE;;GAEG;AACH,cAAM,mBAAmB;IACrB,OAAO,CAAC,SAAS,CAAO;IACxB,OAAO,CAAC,SAAS,CAAS;IAC1B,OAAO,CAAC,SAAS,CAAmB;IACpC,OAAO,CAAC,UAAU,CAAiB;IAEnC,OAAO,CAAC,qBAAqB,CAAmB;IAGhD,OAAO,CAAC,gBAAgB,CAAsE;IAE9F,OAAO,CAAC,gBAAgB,CAA8D;IACtF,OAAO,CAAC,aAAa,CAAkC;IAEvD,OAAO,CAAC,sBAAsB,CAAgC;IAE9D,OAAO,CAAC,mBAAmB,CAAoD;IAC/E,OAAO,CAAC,UAAU,CAA4C;gBAElD,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,cAAc;IAQ/D,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM;IAcjC,IAAI;IA6GX,IAAI,QAAQ,SAEX;IAED,IAAI,QAAQ,WAEX;IAEM,gBAAgB,CAAC,IAAI,EAAE,aAAa,GAAG,kBAAkB;IAMzD,+BAA+B,CAClC,QAAQ,EAAE,kBAAkB,EAC5B,SAAS,EAAE,UAAU,EACrB,KAAK,EAAE,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC,UAAU,GAAG,SAAS,CAAC,SAAS;IAatE,kBAAkB,CAAC,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE;IAWnE,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;IAqBjD,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;IAUjD,kBAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,GAAG,wBAAwB;IACjF,kBAAkB,CAAC,IAAI,EAAE,MAAM,GAAG,wBAAwB,GAAG,SAAS;IAStE,cAAc,CAAC,WAAW,EAAE,wBAAwB;IAMpD,WAAW,CAAC,KAAK,EAAE,wBAAwB;IAc3C,oBAAoB,CAAC,WAAW,EAAE,wBAAwB;IAU1D,gBAAgB,CAAC,KAAK,EAAE,wBAAwB;IAUhD,sBAAsB;IAYhB,WAAW,IAAI,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC;IAsFnE;;OAEG;IACI,mBAAmB;IAiB1B;;OAEG;IACU,uBAAuB;IAuC7B,oBAAoB;IAe3B;;OAEG;IACU,gCAAgC;IAwC7C,OAAO,CAAC,2BAA2B;IAUnC,OAAO,CAAC,mBAAmB;IAM3B,OAAO,CAAC,oBAAoB;IAuB5B,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,4BAA4B,GAAG,oBAAoB,EAAE,MAAM,EAAE,MAAM;IA+FtG;;OAEG;IACU,eAAe;IAU5B;;OAEG;IACI,eAAe;IAWtB;;;OAGG;IACU,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IA4B3D;;;OAGG;IACU,WAAW,CAAC,KAAK,EAAE,MAAM;IAatC;;;OAGG;IACU,cAAc,CAAC,IAAI,EAAE,MAAM;IAoBxC;;;OAGG;IACU,UAAU,CAAC,QAAQ,EAAE,MAAM,GAAG,SAAS;IAoBpD;;;;OAIG;IACU,kBAAkB,CAAC,OAAO,EAAE,MAAM,GAAG,SAAS,EAAE,OAAO,EAAE,MAAM,GAAG,SAAS;IAwDxF;;;;OAIG;IACU,QAAQ,CAAC,OAAO,EAAE,oBAAoB,EAAE,OAAO,EAAE,oBAAoB;IA4ClF;;;;OAIG;IACU,QAAQ,CAAC,OAAO,EAAE,oBAAoB,EAAE,OAAO,EAAE,oBAAoB;IAqDlF;;;;OAIG;IACU,OAAO,CAAC,OAAO,EAAE,mBAAmB,EAAE,OAAO,EAAE,mBAAmB;IAiE/E;;;;OAIG;IACI,SAAS,CAAC,OAAO,EAAE,qBAAqB,EAAE,OAAO,EAAE,qBAAqB;IA6B/E;;;;;;OAMG;IACU,UAAU,CAAC,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM;IAuDxG;;;;OAIG;IACU,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;IA8DzD;;;;OAIG;IACU,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;IA6CzD;;;;;;OAMG;IACU,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,oBAAI,EAAE,UAAU,EAAE,GAAG;IAgCpF;;;;;;OAMG;IACU,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,MAAM,GAAG,MAAM,EAAE;IAgDrG;;;;;OAKG;IACU,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;IA6B1E;;;;;OAKG;IACU,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;IA4B1E;;;;;;OAMG;IACU,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,QAAQ,EAAE,MAAM;IAyBhG;;;;;;OAMG;IACU,WAAW,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,aAAa,EAAE,MAAM;IAmBnG;;;;OAIG;IACU,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;IAqBxD;;;;;OAKG;IACI,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;IAwCjE,OAAO,CAAC,YAAY;IAcpB;;;;OAIG;IACI,sBAAsB,CAAC,SAAS,EAAE,mBAAmB;IAoB5D;;;;OAIG;IACU,yBAAyB,CAAC,QAAQ,EAAE,MAAM;IAevD;;;;OAIG;IACI,yBAAyB,CAAC,QAAQ,EAAE,MAAM;IAUjD;;;;OAIG;IACU,wBAAwB,CAAC,QAAQ,EAAE,MAAM;IAOtD;;;;OAIG;IACU,iBAAiB,CAAC,kBAAkB,EAAE,gBAAgB;IAanE,OAAO,CAAC,kBAAkB;IAa1B;;;;OAIG;IACU,oBAAoB,CAAC,kBAAkB,EAAE,gBAAgB;IAUtE;;;OAGG;IACU,mBAAmB,CAAC,QAAQ,EAAE,MAAM;IAM1C,qBAAqB,CAAC,kBAAkB,EAAE,gBAAgB;IAWjE;;;;;OAKG;IACU,oBAAoB,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,gBAAgB;IAiB1G;;;OAGG;IACH,OAAO,CAAC,YAAY;IAwCpB;;;OAGG;IACI,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE;IAQnC;;;;OAIG;IACI,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE;IA+BlD;;;;OAIG;IACI,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,MAAM;IAkClD;;;;OAIG;IACI,YAAY,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE,QAAQ,EAAE,MAAM;IA4CzD;;;;;;OAMG;IACU,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;IAyBzF;;;;;OAKG;IACU,mBAAmB,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;IAuDjF,OAAO,CAAC,sBAAsB;IAgB9B,OAAO,CAAC,cAAc;IAkBT,kBAAkB;IAiBxB,iBAAiB,CAAC,IAAI,EAAE,MAAM;IAY9B,oBAAoB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;IAsBrD,oBAAoB,CAAC,IAAI,EAAE,MAAM;IAc3B,YAAY,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,eAAe;IAuBtE,YAAY,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;IAexC,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE;IAarE,UAAU,CAAC,GAAG,EAAE,eAAe,EAAE,IAAI,EAAE,gBAAgB;IASvD,mBAAmB,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU;IAejE,uBAAuB,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;IAWhE,OAAO,CAAC,aAAa;CAuBxB;AAED,eAAe,mBAAmB,CAAC"}