{"version": 3, "file": "type-defines.d.ts", "sourceRoot": "", "sources": ["../../../../../source/script/3d/manager/animation/type-defines.ts"], "names": [], "mappings": "AAAA,OAAO,EACH,SAAS,EACT,YAAY,EACf,MAAM,+BAA+B,CAAC;AAEvC,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,SAAS,EAAkB,IAAI,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,MAAM,IAAI,CAAC;AAE5I,KAAK,kBAAkB,GAAG,SAAS,CAAC,kBAAkB,CAAC;AACvD,KAAK,uBAAuB,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AACnD,KAAK,cAAc,GAAG,SAAS,CAAC,UAAU,CAAC;AAE3C,UAAU,SAAS;IACf,YAAY,CAAC,EAAE,SAAS,CAAC;IACzB,IAAI,CAAC,EAAE,SAAS,CAAC;IACjB,WAAW,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC;IAClC,YAAY,CAAC,EAAE,kBAAkB,CAAC;IAClC,YAAY,CAAC,EAAE,GAAG,CAAC;CACtB;AAED,UAAU,aAAa;IACnB,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,IAAI,CAAC,EAAE,SAAS,CAAC;IACjB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,QAAQ,CAAC,EAAE,SAAS,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;CACrB;AAED,UAAU,cAAe,SAAQ,aAAa;IAC1C,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC;IACjC,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;CACvB;AAED,UAAU,cAAc;IACpB,KAAK,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,EAAE,CAAC;CACpB;AAED,UAAU,SAAS;IACf,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;IACd,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,EAAE,cAAc,EAAE,CAAC;IACzB,MAAM,EAAE,cAAc,EAAE,CAAC;CAC5B;AAED,UAAU,SAAS;IACf,IAAI,CAAC,EAAE,IAAI,CAAC;IACZ,QAAQ,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC,mBAAmB,CAAC;IACrD,KAAK,CAAC,EAAE,CAAC,aAAa,GAAG,IAAI,CAAC,EAAE,CAAC;IACjC,WAAW,EAAE,aAAa,GAAG,IAAI,CAAC;CACrC;AAED,UAAU,eAAe;IACrB,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;CACpB;AAED,UAAU,kBAAkB;IACxB,UAAU,EAAE,eAAe,CAAC;CAC/B;AAED,UAAU,UAAU;IAChB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,UAAU,CAAC,EAAE,qBAAqB,CAAC;IACnC,iBAAiB,CAAC,EAAE,iBAAiB,CAAC;IACtC,WAAW,CAAC,EAAE,WAAW,CAAC;IAC1B,YAAY,CAAC,EAAE,iBAAiB,CAAC,cAAc,CAAC,CAAC;IACjD,MAAM,CAAC,EAAE,OAAO,CAAC;CACpB;AAED,UAAU,eAAgB,SAAQ,UAAU;IACxC,QAAQ,CAAC,EAAE,GAAG,CAAC;CAClB;AAED,UAAU,SAAU,SAAQ,UAAU;IAClC,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,EAAE,GAAG,CAAC;CACd;AAED,UAAU,UAAU;IAChB,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;IAChB,YAAY,CAAC,EAAE,SAAS,CAAC;IACzB,IAAI,CAAC,EAAE,SAAS,CAAC;IACjB,WAAW,EAAE,SAAS,CAAC,SAAS,CAAC;IACjC,YAAY,CAAC,EAAE,kBAAkB,CAAC;IAClC,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,EAAE,MAAM,CAAC;IACjB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,YAAY,CAAC,EAAE,GAAG,CAAC;CACtB;AAED,UAAU,aAAa;IACnB,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,SAAS,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,cAAc,EAAE,SAAS,CAAC,mBAAmB,CAAC;CACjD;AAED,aAAK,WAAW;IACZ,IAAI,IAAI;IACR,IAAI,IAAI;IACR,KAAK,IAAI;IACT,IAAI,IAAI;CACX;AAED,OAAO,EACH,SAAS,EACT,aAAa,EACb,cAAc,EACd,aAAa,EACb,SAAS,EACT,SAAS,EACT,cAAc,EACd,kBAAkB,EAClB,kBAAkB,EAClB,eAAe,EACf,uBAAuB,EACvB,SAAS,EACT,eAAe,EACf,UAAU,EACV,UAAU,EACV,WAAW,GACd,CAAC"}