{"version": 3, "file": "node.d.ts", "sourceRoot": "", "sources": ["../../../../../source/script/3d/manager/prefab/node.ts"], "names": [], "mappings": "AAAA,OAAO,EAA0C,SAAS,EAA4C,IAAI,EAAE,MAAM,EAAE,KAAK,EAAsB,MAAM,IAAI,CAAC;AAE1J,OAAO,EAAsB,oBAAoB,EAAE,MAAM,aAAa,CAAC;AAKvE,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAQhF,KAAK,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAC3C,QAAA,MAAM,UAAU,iCAA2B,CAAC;AAC5C,KAAK,oBAAoB,GAAG,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC;AAC/D,QAAA,MAAM,oBAAoB,2CAAqC,CAAC;AAChE,KAAK,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;AAGnD,KAAK,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAC3C,QAAA,MAAM,UAAU,iCAA2B,CAAC;AAQ5C,UAAU,iBAAiB;IACvB,QAAQ,EAAE,MAAM,EAAE,CAAC;IACnB,KAAK,EAAE,GAAG,CAAC;CACd;AAoBD,UAAU,eAAe;IACrB,UAAU,EAAE,UAAU,GAAG,IAAI,CAAC;CACjC;AACD,UAAU,0BAA0B;IAChC,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,UAAU,EAAE,UAAU,GAAG,IAAI,CAAC;IAC9B,YAAY,EAAE,MAAM,EAAE,CAAC;IACvB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,UAAU,EAAE,UAAU,GAAG,IAAI,CAAC;CACjC;AAED,UAAU,gBAAgB;IACtB,QAAQ,EAAE,MAAM,CAAC;IACjB,sBAAsB,EAAE,GAAG,CAAC,MAAM,EAAE,EAAE,eAAe,CAAC,CAAC;IACvD,wBAAwB,EAAE,GAAG,CAAC,MAAM,EAAE,EAAE,oBAAoB,CAAC,CAAC;IAC9D,iBAAiB,EAAE,oBAAoB,EAAE,CAAC;IAC1C,iBAAiB,EAAE,UAAU,EAAE,CAAC;IAChC,iBAAiB,EAAE,GAAG,CAAC;IACvB,eAAe,EAAE,0BAA0B,EAAE,CAAC;CACjD;AA2CD,cAAM,aAAa;IACR,eAAe,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAa;IACjD,yBAAyB,UAAS;IAEzC,OAAO,CAAC,UAAU,CAA8B;IAEzC,aAAa,CAAC,KAAK,EAAE,GAAG;IAsBxB,aAAa,CAAC,IAAI,EAAE,IAAI;IAe/B,OAAO,CAAC,mBAAmB;IA6DpB,0BAA0B,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,IAAI,GAAG,KAAK,GAAG,IAAI;IA+BjG;;;;;OAKG;IACI,sBAAsB,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,GAAG,KAAK,GAAG,IAAI;IA0B9E,SAAS,CAAC,IAAI,EAAE,IAAI;IAWpB,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW;IAiChD;;;;;;OAMG;IACI,wBAAwB,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,iBAAiB,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,GAAG,OAAO;IAyG3G,OAAO,CAAC,2BAA2B;IAsEnC,OAAO,CAAC,mBAAmB;IAQ3B,OAAO,CAAC,qBAAqB;IAI7B;;;;;;;OAOG;IACH,OAAO,CAAC,oBAAoB;IAqC5B,OAAO,CAAC,uBAAuB;IA4E/B;;;;OAIG;IACH,OAAO,CAAC,+BAA+B;IAqBvC,OAAO,CAAC,wBAAwB;IAiBhC;;;;OAIG;IACI,2BAA2B,CAAC,YAAY,EAAE,MAAM,CAAC,MAAM,CAAC,oBAAoB,EAAE,gBAAgB,EAAE,MAAM;IAY7G;;;;OAIG;IACI,+BAA+B,CAAC,cAAc,EAAE,cAAc,EAAE,gBAAgB,EAAE,MAAM;IAaxF,oBAAoB,CAAC,IAAI,EAAE,IAAI;IA8F/B,sBAAsB,CAAC,IAAI,EAAE,IAAI;IAmFjC,oBAAoB,CAAC,IAAI,EAAE,IAAI;IA8G/B,sBAAsB,CAAC,IAAI,EAAE,IAAI;cA8ExB,kBAAkB;IAKlC;;;OAGG;IACU,WAAW,CAAC,QAAQ,EAAE,MAAM;IAoB5B,aAAa,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAwDjE,eAAe,CAAC,eAAe,EAAE,gBAAgB;IAmHvD,kBAAkB,CAAC,IAAI,EAAE,IAAI;IA4GpC,OAAO,CAAC,2BAA2B;IAsD5B,sBAAsB,CAAC,IAAI,EAAE,IAAI,GAAG,OAAO;IA2BlD;;;OAGG;IACU,yBAAyB,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,UAAO;IA4DjF;;;;OAIG;IACI,uBAAuB,CAAC,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAyBhF;;;;;OAKG;IACU,yCAAyC,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IA2B7H;;;;OAIG;IACU,uBAAuB,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI,EAAE,SAAS,EAAE,MAAM,GAAG,GAAG;IAgFrF;;;;OAIG;IACI,qBAAqB,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI;IA8CtD,cAAc,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI;IAmF7D,+BAA+B,CAAC,IAAI,EAAE,IAAI;IAmB1C,8BAA8B,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,kBAAkB;IAyDnE,sBAAsB,CAAC,YAAY,EAAE,MAAM,CAAC,MAAM,CAAC,oBAAoB,EAAE,gBAAgB,EAAE,GAAG,EAAE,cAAc,EAAE,GAAG;IA+D1H;;;OAGG;IACU,YAAY,CAAC,QAAQ,EAAE,IAAI,GAAG,MAAM;IA8H1C,wBAAwB,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,EAAE,OAAO;IAkB3D,gCAAgC,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,EAAE,OAAO,GAAG,OAAO;IAmD7E,iCAAiC,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,CAAC,EAAE,OAAO;IA6C3F;;;;OAIG;IACI,oBAAoB,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI,EAAE,YAAY,CAAC,EAAE,OAAO,GAAG,OAAO;IA2B9E,gCAAgC,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI,EAAE,YAAY,CAAC,EAAE,OAAO,GAAG,OAAO;CAiEpG;AAED,QAAA,MAAM,aAAa,eAAsB,CAAC;AAE1C,OAAO,EAAE,aAAa,EAAE,eAAe,EAAE,gBAAgB,EAAE,CAAC"}