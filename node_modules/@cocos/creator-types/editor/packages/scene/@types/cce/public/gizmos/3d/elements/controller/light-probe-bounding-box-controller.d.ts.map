{"version": 3, "file": "light-probe-bounding-box-controller.d.ts", "sourceRoot": "", "sources": ["../../../../../../../source/script/public/gizmos/3d/elements/controller/light-probe-bounding-box-controller.ts"], "names": [], "mappings": "AACA,OAAO,EAGH,eAAe,EACf,IAAI,EAEJ,IAAI,EAEP,MAAM,IAAI,CAAC;AAEZ,OAAO,EAAE,kBAAkB,EAAE,MAAM,kBAAkB,CAAC;AAGtD,OAAO,0BAA0B,MAAM,4CAA4C,CAAC;AACpF,OAAO,aAAa,MAAM,kBAAkB,CAAC;AAC7C,OAAO,EAAE,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAa1D,MAAM,CAAC,OAAO,OAAO,+BAAgC,SAAQ,aAAc,YAAW,0BAA0B;IAmBjG,cAAc,EAAE,cAAc,CAAC,eAAe,CAAC;IAlB1D,SAAS,UAAQ;IAEjB,OAAO,CAAC,iBAAiB,CAAoB;IAC7C,OAAO,CAAC,OAAO,CAAoB;IACnC,OAAO,CAAC,OAAO,CAAoB;IAEnC,OAAO,CAAC,SAAS,CAAS;IAC1B,IAAW,QAAQ,IAAI,OAAO,CAE7B;IAED,OAAO,CAAC,aAAa,CAAsD;IAC3E,OAAO,CAAC,cAAc,CAA0D;IAChF,OAAO,CAAC,wBAAwB,CAAM;IACtC,OAAO,CAAC,kBAAkB,CAAS;gBAG/B,QAAQ,EAAE,IAAI,EACP,cAAc,EAAE,cAAc,CAAC,eAAe,CAAC;IAQ1D,iBAAiB;IAQjB,IAAI;IAKJ,gBAAgB;IAShB,mBAAmB;IAYnB,WAAW,CAAC,KAAK,EAAE,kBAAkB;IAOrC,WAAW,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI;IAK5C,SAAS,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI;IAK1C,0BAA0B,CAAC,KAAK,EAAE,kBAAkB;IAcpD,MAAM;IAwCN,MAAM;IASN,oBAAoB,IAAI,IAAI;IAI5B,oBAAoB,CAAC,MAAM,EAAE,IAAI;IAQjC,yBAAyB,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI;IAK9C,0BAA0B,CAAC,IAAI,EAAE,OAAO;CAU3C"}