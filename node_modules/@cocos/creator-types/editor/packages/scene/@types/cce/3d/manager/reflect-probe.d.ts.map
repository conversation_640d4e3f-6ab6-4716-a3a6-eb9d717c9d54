{"version": 3, "file": "reflect-probe.d.ts", "sourceRoot": "", "sources": ["../../../../source/script/3d/manager/reflect-probe.ts"], "names": [], "mappings": "AAEA,OAAO,EAAU,SAAS,EAAY,IAAI,EAAmB,MAAM,IAAI,CAAC;AAIxE,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AAI7E,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AAExD,qBAAa,mBAAoB,YAAW,YAAY;IACpD,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,mBAAmB,CAAC;IAC/C,MAAM,CAAC,YAAY,IAAI,mBAAmB;IAG1C,SAAS;IAGT,QAAQ,CAAC,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI;IAG1B,aAAa,CAAC,CAAC,KAAK,EAAE,GAAG,GAAG,IAAI;IAChC,aAAa,CAAC,CAAC,KAAK,EAAE,GAAG,GAAG,IAAI;IAChC,aAAa,CAAC,CAAC,KAAK,EAAE,GAAG,GAAG,IAAI;IAKhC,aAAa,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,kBAAkB,GAAG,IAAI;IAC1D,SAAS,CAAC,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI;IAC5B,YAAY,CAAC,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI;IAC/B,WAAW,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,GAAG,IAAI;IACjD,aAAa,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,GAAG,IAAI;IAGnD,cAAc,CAAC,CAAC,IAAI,EAAE,SAAS,GAAG,IAAI;IACtC,iBAAiB,CAAC,CAAC,IAAI,EAAE,SAAS,GAAG,IAAI;IACzC,gBAAgB,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,EAAE,WAAW,GAAG,IAAI;IAC5D,kBAAkB,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,EAAE,WAAW,GAAG,IAAI;IAG9D,cAAc,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI;IAC/C,cAAc,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI;IAEpD,IAAI;IACX,SAAS,CAAC,UAAU,EAAE;QAAC,IAAI,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,IAAI,CAAA;KAAC,EAAE,CAAM;IACzE,SAAS,CAAC,OAAO,UAAS;IACnB,UAAU;IAKV,QAAQ,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAGtC,IAAW,MAAM,IAAI,OAAO,CAE3B;IACD,SAAS,KAAK,MAAM,CAAC,KAAK,EAHL,OAGK,EAEzB;IACD,SAAS,CAAC,kBAAkB,SAAM;IAC3B,YAAY,CAAC,UAAU,EAAE,aAAa,CAAC,MAAM,CAAC,GAAG,IAAI;IAO/C,WAAW,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IA2B1D;;;OAGG;cACa,WAAW,CAAC,SAAS,EAAE,MAAM;IA+BvC,gBAAgB;IAShB,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ;CA6HpH;;AAED,wBAAkD"}