{"version": 3, "file": "controller-light-probe.d.ts", "sourceRoot": "", "sources": ["../../../../../../source/script/public/gizmos/components/light-probe-group/controller-light-probe.ts"], "names": [], "mappings": "AAAA,OAAO,cAAc,MAAM,uBAAuB,CAAC;AAEnD,OAAO,EACH,KAAK,EAKL,YAAY,EACZ,IAAI,EAGJ,IAAI,EAGP,MAAM,IAAI,CAAC;AACZ,OAAO,KAAK,EAAwB,eAAe,EAAE,MAAM,qBAAqB,CAAC;AAEjF,OAAO,2BAA2B,MAAM,4CAA4C,CAAC;AACrF,OAAO,EAAE,gBAAgB,EAAE,MAAM,kCAAkC,CAAC;AAEpE,OAAO,SAAoB,MAAM,sBAAsB,CAAC;AACxD,OAAO,KAAK,eAAe,MAAM,gBAAgB,CAAC;AAClD,OAAO,KAAK,EAAE,uBAAuB,EAAE,MAAM,gBAAgB,CAAC;AAK9D,MAAM,MAAM,SAAS,GAAG;IACpB,IAAI,EAAE,IAAI,CAAC;IACX,KAAK,EAAE,IAAI,CAAC;IACZ,YAAY,EAAE,YAAY,GAAG,IAAI,CAAC;CACrC,CAAA;AAED,MAAM,CAAC,OAAO,OAAO,oBAAqB,SAAQ,cAAe,YAAW,2BAA2B;IA8ExF,KAAK,EAAE,eAAe;IA5EjC,MAAM,CAAC,UAAU,QAAuB;IACxC,MAAM,CAAC,kBAAkB,QAAuB;IAChD,MAAM,CAAC,cAAc,QAAuB;IAC5C,MAAM,CAAC,WAAW,kBAAe;IAEjC,MAAM,CAAC,KAAK,SAAK;IACjB,MAAM,KAAK,IAAI,IAAI,MAAM,CAExB;IAGD,SAAS,CAAC,SAAS,UAAQ;IAE3B,OAAO,CAAC,cAAc,CAA+B;IAGrD,OAAO,CAAC,cAAc,CAAS;IAC/B,OAAO,CAAC,UAAU,CAAc;IAChC,OAAO,CAAC,cAAc,CAAwD;IAC9E,OAAO,CAAC,0BAA0B,CAA8C;IAChF,OAAO,CAAC,YAAY,CAAqD;IACzE,OAAO,CAAC,cAAc,CAAC,CAAO;IAC9B,OAAO,CAAC,uBAAuB,CAAS;IACxC,OAAO,CAAC,SAAS,CAAS;IAC1B,OAAO,CAAC,eAAe,CAAkB;IAGzC,OAAO,CAAC,mBAAmB,CAAC,CAAoC;IAChE,OAAO,CAAC,wBAAwB,CAAM;IAEtC,MAAM,CAAC,uBAAuB,EAAE,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAgC;IACtF,MAAM,CAAC,oBAAoB,SAAsB;IAEjD,OAAO,CAAC,eAAe,CAA0D;IACjF,MAAM,CAAC,SAAS,EAAE,IAAI,CAA6C;IAGnE,OAAO,CAAC,cAAc,CAAwC;IAC9D,IAAI,aAAa,CAAC,aAAa,EAAE,uBAAuB,GAAG,IAAI,EAe9D;IACD,IAAI,aAAa,IAAI,uBAAuB,GAAG,IAAI,CAElD;IAED,IAAW,kBAAkB,IAAI;QAAE,GAAG,EAAE,MAAM,CAAC;QAAC,SAAS,EAAE,SAAS,CAAA;KAAE,EAAE,CAQvE;IAED,IAAW,WAAW,IAAI,IAAI,CAE7B;IAED,OAAO,CAAC,gBAAgB,CAAc;gBAGlC,QAAQ,EAAE,IAAI,EACP,KAAK,EAAE,eAAe;IAOjC,SAAS;IAWT,oBAAoB;IAkBb,IAAI,IAAI,IAAI;IAQnB,gBAAgB;IAuBhB,YAAY;IAuBZ,WAAW;IAUX,eAAe,IAAI,GAAG,CAAC,MAAM,CAAC;IAqC9B,YAAY,CAAC,aAAa,GAAE,GAAG,CAAC,MAAM,CAAqB,GAAG,MAAM,EAAE;IA+CtE,oBAAoB;IAIpB,eAAe,CAAC,KAAK,EAAE,IAAI,GAAG,IAAI;IAsC3B,iBAAiB;IAUxB,MAAM,CAAC,SAAS,QAAa;IAC7B,MAAM,CAAC,oBAAoB,SAAe;IAC1C;;OAEG;IACH,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS;IAmB3D,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,KAAK,GAAE,KAA2C;IAMnI,MAAM,CAAC,gBAAgB,WAOrB;IAEF,MAAM,CAAC,cAAc,WASnB;IAEF,iBAAiB,CAAC,UAAU,EAAE,SAAS,EAAE;IAiBzC,uBAAuB,CAAC,IAAI,EAAE,IAAI;IAOlC,SAAS,CAAC,MAAM;IAUhB,SAAS,CAAC,MAAM;IAEhB,SAAS,CAAC,WAAW,CAAC,KAAK,EAAE,eAAe;IAK5C,SAAS,CAAC,WAAW,CAAC,KAAK,EAAE,eAAe;IAK5C,SAAS,CAAC,SAAS,CAAC,KAAK,EAAE,eAAe;IAK1C,IAAI,qBAAqB,IAAI,SAAS,EAAE,CAIvC;IAEM,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;IAOnC,aAAa,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;IAO5C,OAAO,CAAC,yBAAyB;IAUjC,OAAO,CAAC,mBAAmB;IAsB3B,yBAAyB,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI;IAiB9C,0BAA0B,CAAC,IAAI,EAAE,OAAO;IAQxC,8BAA8B,IAAI,SAAS,CAAC,IAAI,CAAC;IAmBjD,2BAA2B,IAAI,SAAS,CAAC,IAAI,CAAC;IAqB9C,UAAU,CAAC,IAAI,EAAE,IAAI;IAKrB,YAAY,CAAC,IAAI,EAAE,IAAI;IAKvB,oBAAoB;IAIpB,sBAAsB;IAItB,IAAI,IAAI,SAAS,EAAE;IAkBnB,mBAAmB,CAAC,KAAK,EAAE,gBAAgB,GAAG,IAAI;IAGlD,mBAAmB,CAAC,KAAK,EAAE,gBAAgB,GAAG,IAAI;IAGlD,iBAAiB,CAAC,KAAK,EAAE,gBAAgB,GAAG,IAAI;IAGhD,eAAe,IAAI,SAAS,CAAC,MAAM,CAAC;IAIpC,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI;IAMpC,SAAS,IAAI,IAAI;IAMjB,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI;IAMtC,WAAW,IAAI,IAAI;IAMnB,oBAAoB,IAAI,SAAS,CAAC,IAAI,CAAC;IAIvC,qBAAqB;IAKrB,eAAe;CAMlB;AAwDD,UAAU,eAAe;IAGrB,SAAS,EAAE,OAAO,CAAC;IACnB,aAAa,EAAE,OAAO,CAAC;IACvB,UAAU,EAAE,OAAO,CAAC;IACpB,sBAAsB,EAAE,MAAM,CAAC;CAClC;AAED,cAAM,cAAc;IAKL,SAAS,EAAE,OAAO;IAClB,aAAa,EAAE,OAAO;IACtB,UAAU,EAAE,OAAO;IACnB,sBAAsB,EAAE,MAAM;gBAH9B,SAAS,GAAE,OAAc,EACzB,aAAa,GAAE,OAAc,EAC7B,UAAU,GAAE,OAAe,EAC3B,sBAAsB,GAAE,MAAY;IAG/C,MAAM,CAAC,IAAI,CAAC,EAAE,eAAe;CAQhC;AAED,QAAA,MAAM,cAAc,gBAAuB,CAAC;AAiD5C,OAAO,EAAE,cAAc,EAAE,CAAC"}