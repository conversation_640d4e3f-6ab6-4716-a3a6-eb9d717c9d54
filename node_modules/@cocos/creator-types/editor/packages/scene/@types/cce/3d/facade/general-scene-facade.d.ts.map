{"version": 3, "file": "general-scene-facade.d.ts", "sourceRoot": "", "sources": ["../../../../source/script/3d/facade/general-scene-facade.ts"], "names": [], "mappings": ";;;;;AAEA,OAAO,EACH,sBAAsB,EACtB,iBAAiB,EACjB,cAAc,EACd,6BAA6B,EAC7B,+BAA+B,EAC/B,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAChB,mBAAmB,EACnB,kBAAkB,EAClB,sBAAsB,EACtB,iBAAiB,EACjB,kBAAkB,EAClB,gBAAgB,EACnB,MAAM,2BAA2B,CAAC;AAEnC,OAAO,EAAE,cAAc,EAAE,kBAAkB,EAAE,WAAW,EAAE,iBAAiB,EAA0B,MAAM,4BAA4B,CAAC;AACxI,OAAO,EAAE,iBAAiB,EAAE,aAAa,EAAE,MAAM,gCAAgC,CAAC;AAclF,OAAO,UAAU,MAAM,oCAAoC,CAAC;AAG5D,OAAO,EAAE,SAAS,EAAY,SAAS,EAAE,IAAI,EAAoB,MAAM,IAAI,CAAC;AAC5E,OAAO,EAAE,YAAY,EAAE,MAAM,mCAAmC,CAAC;AAUjE,OAAO,EACH,+BAA+B,EAC/B,0BAA0B,EAC1B,6BAA6B,EAEhC,MAAM,4CAA4C,CAAC;AACpD,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,6DAA6D,CAAC;AACrG,OAAO,EAAoB,kBAAkB,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAMlG,qBAAa,kBAAmB,YAAW,iBAAiB;IACxD,SAAS,CAAC,SAAS,mDAAY;IAC/B,SAAS,CAAC,UAAU,qCAAa;IACjC,SAAS,CAAC,QAAQ,wCAAW;IAC7B,SAAS,CAAC,QAAQ,6CAAW;IAC7B,SAAS,CAAC,SAAS,0CAAY;IAC/B,SAAS,CAAC,SAAS,2DAAY;IAC/B,SAAS,CAAC,mBAAmB,wDAA8B;IAC3D,SAAS,CAAC,eAAe,gDAA0B;IACnD,SAAS,CAAC,UAAU,6CAAa;IACjC,SAAS,CAAC,WAAW,8CAAc;IACnC,SAAS,CAAC,aAAa,kDAAgB;IACvC,SAAS,CAAC,aAAa,gDAAgB;IACvC,SAAS,CAAC,UAAU,6CAAa;IACjC,SAAS,CAAC,WAAW,8CAAc;IACnC,SAAS,CAAC,UAAU,4CAAa;IACjC,SAAS,CAAC,UAAU,4CAAa;IACjC,SAAS,CAAC,UAAU,4CAAa;IACjC,SAAS,CAAC,MAAM,oCAAS;IACzB,SAAS,CAAC,YAAY,gDAAe;IACrC,SAAS,CAAC,OAAO,sCAAU;IAC3B,SAAS,CAAC,gBAAgB,kDAA2B;IACrD,SAAS,CAAC,eAAe,gDAA0B;IACnD,SAAS,CAAC,aAAa,mDAAgB;IACvC,SAAS,CAAC,cAAc,qDAAiB;IACzC,SAAS,CAAC,WAAW,EAAG,UAAU,CAAC;IAGnC,SAAS,CAAC,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAM;IAClD,SAAS,CAAC,iBAAiB,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAM;IACrD,SAAS,CAAC,EAAE,iBAAiB,CAAC;IAGrC,SAAS,CAAC,QAAQ,EAAE,iBAAiB,CAA0B;IAIxD,MAAM,UAAS;IACf,QAAQ,EAAE,aAAa,CAAyB;IAChD,kBAAkB,UAAS;IAGlC,SAAS,CAAC,mBAAmB,EAAE,YAAY,EAAE,CAAM;IAE5C,IAAI;IASJ,iBAAiB;IAgBX,KAAK,CAAC,IAAI,EAAE,GAAG;IAwBrB,SAAS;IAMH,IAAI;IAKV,qBAAqB;IAIf,YAAY,IAAI,OAAO,CAAC,OAAO,CAAC;IAIhC,eAAe,IAAI,OAAO,CAAC,OAAO,CAAC;IAQnC,iBAAiB,IAAI,OAAO,CAAC,OAAO,CAAC;IAK3C,cAAc;IAIR,SAAS,CAAC,IAAI,EAAE,MAAM;IAgCtB,UAAU;IASV,SAAS,CAAC,KAAK,EAAE,OAAO;IAK9B,cAAc;IAGR,eAAe;IAIf,iBAAiB,CAAC,IAAI,CAAC,EAAE,GAAG;IAI5B,eAAe,CAAC,IAAI,CAAC,EAAE,GAAG;IAM1B,WAAW;IAIX,aAAa,CAAC,IAAI,EAAE,MAAM;IAI1B,qBAAqB,CAAC,IAAI,EAAE,MAAM;IAIlC,wBAAwB,IAAI,OAAO,CAAC,MAAM,CAAC;IAG3C,eAAe;IAIf,YAAY,CAAC,OAAO,CAAC,EAAE,mBAAmB;;;IAI1C,eAAe;;;;;;IAIf,uBAAuB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAIvD,iBAAiB;;;;IAIjB,wBAAwB;IAIrC;;OAEG;IACI,SAAS;IAIT,qBAAqB;IAIrB,cAAc,CAAC,SAAS,EAAE,MAAM,YAAY,EAAE,GAAG,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC;IAS/D,aAAa,CAAC,KAAK,EAAE,GAAG;IAQxB,aAAa,CAAC,KAAK,EAAE,GAAG;IAIxB,aAAa,CAAC,KAAK,EAAE,GAAG;IAIxB,QAAQ,CAAC,IAAI,EAAE,SAAS;IAOxB,gBAAgB,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI;IAI3C,qBAAqB,CAAC,IAAI,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI;IAI/C,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAIzC,4BAA4B,CAAC,IAAI,EAAE,MAAM;IAIzC,sBAAsB,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC;IAIrE,4BAA4B,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC;IAI3E,eAAe,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC;IAI9D,SAAS,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAIzC,iBAAiB,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC;IAIhE,0BAA0B,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC;IAIzE,uBAAuB,CAAC,OAAO,EAAE,kBAAkB;IAInD,oBAAoB,CAAC,OAAO,EAAE,gBAAgB;IAU9C,sBAAsB,CAAC,OAAO,EAAE,kBAAkB;IAIlD,yBAAyB,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAInF,cAAc,IAAI,IAAI;IAQtB,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE;IAI5C,aAAa,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE;IAqC3C,SAAS,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IA8EvD,aAAa,CAAC,OAAO,EAAE,cAAc,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAQzD,UAAU,CAAC,OAAO,EAAE,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC;IAYpD,UAAU,CAAC,OAAO,EAAE,iBAAiB;IAmCrC,cAAc,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO;IAIvE,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAItE,mBAAmB,CAAC,IAAI,EAAE,IAAI;IAI9B,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,GAAE,kBAAuB;IAUvD,SAAS,CAAC,IAAI,EAAE,IAAI;IASpB,YAAY,CAAC,IAAI,EAAE,IAAI;IAavB,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,GAAE,WAAgB;IAa9C,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW;IAgBrC,cAAc,CAAC,IAAI,EAAE,MAAM;IAIjC,eAAe,CAAC,OAAO,EAAE,sBAAsB;IAIzC,cAAc,CAAC,IAAI,EAAE,MAAM;IAI3B,eAAe,CAAC,OAAO,EAAE,sBAAsB;IAI/C,sBAAsB,CAAC,OAAO,EAAE,6BAA6B,GAAG,OAAO,CAAC,OAAO,CAAC;IAIhF,wBAAwB,CAAC,OAAO,EAAE,+BAA+B;IAIvE,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,GAAE,WAAgB;IAKtD,iBAAiB,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,GAAE,WAAgB;IAMzD,gBAAgB,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,GAAE,WAAgB;IAQxD,kBAAkB,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,GAAE,WAAgB;IAUpD,QAAQ,CAAC,OAAO,CAAC,EAAE,GAAG;IAK5B,aAAa;IAMb,cAAc,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,EAAE,OAAO,CAAC,EAAE,iBAAiB,GAAG,kBAAkB;IAIzF,eAAe,CAAC,SAAS,EAAE,kBAAkB,GAAG,OAAO;IAIvD,YAAY,CAAC,SAAS,EAAE,kBAAkB,GAAG,OAAO;IAM9C,IAAI;IAGJ,IAAI;IAIV,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,UAAO;IAU9B,eAAe,IAAI,OAAO,CAAC,GAAG,CAAC;IAI/B,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAIzC,WAAW,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAI7C,mBAAmB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAI/C,oBAAoB,CAAC,IAAI,EAAE,GAAG;IAI9B,mBAAmB,CAAC,IAAI,EAAE,MAAM;IAIhC,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE;QAAE,IAAI,CAAC,EAAE,OAAO,CAAA;KAAE;IAItE,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG;IAI7C,qBAAqB,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAI9C,oBAAoB,CAAC,IAAI,EAAE,MAAM;IAIjC,2BAA2B,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAIpD,0BAA0B,CAAC,IAAI,EAAE,MAAM;IAIvC,mBAAmB,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAI5C,kBAAkB,CAAC,IAAI,EAAE,MAAM;IAI/B,kBAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG;IAIpD,oBAAoB,CAAC,IAAI,EAAE,MAAM;IAIjC,0BAA0B,CAAC,IAAI,EAAE,MAAM;IAIvC,kBAAkB,CAAC,IAAI,EAAE,MAAM;IAI/B,wBAAwB;IAIlB,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU;IAIlE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;IAInC,YAAY,CAAC,IAAI,EAAE,MAAM;IAQnB,kBAAkB,IAAI,OAAO,CAAC,IAAI,CAAC;IAIzC,kBAAkB,IAAI,MAAM;IAI5B,kBAAkB,IAAI,MAAM;IAItB,eAAe,IAAI,OAAO,CAAC,MAAM,CAAC;IAIlC,oBAAoB,IAAI,OAAO,CAAC,MAAM,CAAC;IAIvC,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC;IAInC,kBAAkB,IAAI,OAAO;IAI7B,kBAAkB,IAAI,MAAM;IAI5B,sBAAsB;IAIhB,oBAAoB,CAAC,IAAI,EAAE,6BAA6B;IAQ9D,eAAe;IAIT,QAAQ,CAAC,IAAI,EAAE,0BAA0B;IAIzC,aAAa,CAAC,IAAI,EAAE,+BAA+B;IAInD,OAAO,CAAC,KAAK,EAAE,OAAO;IAItB,cAAc,CAAC,IAAI,EAAE,OAAO;IAI5B,gBAAgB,CAAC,IAAI,EAAE,MAAM;IAI7B,oBAAoB,CAAC,OAAO,EAAE,OAAO;IAO3C,KAAK,CAAC,IAAI,GAAE,MAAM,EAAE,GAAG,IAAW,EAAE,gBAAgB,CAAC,EAAE,gBAAgB,EAAE,SAAS,CAAC,EAAE,OAAO;IAI5F,oBAAoB,CAAC,KAAK,EAAE,MAAM,EAAE;IAIpC,kBAAkB;IAIlB,cAAc,CAAC,OAAO,EAAE,OAAO;IAI/B,aAAa;IAOb,iBAAiB;IAOjB,gBAAgB,CAAC,KAAK,EAAE,MAAM,EAAE;IAIhC,iBAAiB,IAAI,GAAG;IAGxB,iBAAiB,CAAC,IAAI,EAAE,GAAG;IAG3B,mBAAmB;IAInB,mBAAmB,IAAI,MAAM;IAG7B,mBAAmB,CAAC,KAAK,EAAE,MAAM;IAIjC,oBAAoB,IAAI,MAAM;IAG9B,oBAAoB,CAAC,KAAK,EAAE,MAAM;IAIlC,eAAe,IAAI,IAAI;IAGvB,iBAAiB,IAAI,IAAI;IAGzB,kBAAkB,IAAI,IAAI;IAG1B,wBAAwB,IAAI,IAAI;IAUhC,0BAA0B,IAAI,IAAI;IA+BlC,oBAAoB,IAAI,IAAI;IA2B5B,0BAA0B,IAAI,GAAG;IAIjC,yBAAyB,IAAI,GAAG;IAGhC,sBAAsB,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;IAG5C,sBAAsB,CAAC,IAAI,EAAE,MAAM,GAAG,GAAG;IAGzC,sBAAsB,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,GAAG;IAG/D,wBAAwB,CAAC,IAAI,EAAE,MAAM,GAAG,GAAG;IAG3C,uBAAuB,CAAC,QAAQ,EAAE,MAAM,GAAG,GAAG;IAG9C,6BAA6B,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM;IAGvD,8BAA8B,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,GAAG;IAIvG,yBAAyB,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;IAIjE,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAKnF,uBAAuB,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAGzE,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAI9C,eAAe,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAIpE,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAG/C,QAAQ,IAAI,OAAO,CAAC,OAAO,CAAC;IAI5B,uBAAuB,CAAC,aAAa,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC,cAAc,CAAC;IAOvF,0BAA0B,CAAC,IAAI,EAAE,MAAM;IAIvC,oBAAoB,CAAC,QAAQ,EAAE,MAAM;IAO/B,eAAe,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAI3C,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAI1C,UAAU,CAAC,IAAI,EAAE,MAAM;IAIvB,YAAY,CAAC,IAAI,EAAE,UAAU;IAI7B,YAAY,CAAC,IAAI,EAAE,UAAU;IAOnC,cAAc,IAAI,MAAM,EAAE;IAG1B,YAAY,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAGnC,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAG9B,YAAY,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAGhC,cAAc,IAAI,IAAI;IAOtB,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI;IAItC,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI;IAIpC,YAAY,CAAC,IAAI,EAAE,MAAM;IAOzB,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,IAAI;IAOxC,YAAY,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM;IAG5C,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,GAAG;IAIhC,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,GAAG;IAGpD,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,GAAG,GAAG;IAY1D,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,GAAG,GAAG;IAItD,WAAW,CAAC,QAAQ,EAAE,MAAM;IAOzC,qBAAqB,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAIzC,gBAAgB,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAMpC,qBAAqB,CAAC,IAAI,EAAE,MAAM;;;;;;IAGlC,oBAAoB,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;IAGhD;;;OAGG;IACI,YAAY;IAGnB;;;OAGG;IACI,eAAe;IAGtB;;;OAGG;IACI,aAAa;IAGpB;;;OAGG;IACI,YAAY;IAQnB,sBAAsB,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC;IAG7C,SAAS,CAAC,YAAY,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,UAAU,CAAC,OAAO,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI;IAG9F,QAAQ,CAAC,YAAY,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,UAAU,CAAC,OAAO,IAAI,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI;IAmBtF,kBAAkB;IA6CjB,cAAc;IASd,yBAAyB,CAAC,IAAI,EAAE,MAAM;IAOhC,mBAAmB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAO5D,8BAA8B;IAI9B,2BAA2B;IAM3B,wBAAwB,CAAC,IAAI,EAAE,OAAO,GAAG,SAAS;IAWlD,uBAAuB,IAAI,OAAO;IAKlC,mCAAmC,CAAC,IAAI,EAAE,OAAO,GAAG,SAAS;IAW7D,kCAAkC,IAAI,OAAO;IAKvC,WAAW;CAOpB;AAED,eAAe,kBAAkB,CAAC"}