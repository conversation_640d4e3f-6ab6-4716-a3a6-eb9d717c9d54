{"version": 3, "file": "terrain-gizmo.d.ts", "sourceRoot": "", "sources": ["../../../../../../../../source/script/public/gizmos/3d/elements/components/terrain/terrain-gizmo.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,MAAM,kBAAkB,CAAC;AACrC,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AACjD,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAE3D,OAAO,EAAE,mBAAmB,EAAE,MAAM,wCAAwC,CAAC;AAC7E,OAAO,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAC;AAOzD,UAAU,MAAM;IACZ,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE,MAAM,CAAC;CACtB;AAED,UAAU,YAAY;IAClB,QAAQ,EAAE,MAAM,CAAC;IACjB,aAAa,EAAE,MAAM,CAAC;IACtB,YAAY,EAAE,MAAM,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;CACtB;AAED,cAAM,YAAa,SAAQ,KAAK;IAC5B,OAAO,CAAC,OAAO,CAA8B;IAC7C,OAAO,CAAC,WAAW,CAAkC;IAErD,OAAO,CAAC,aAAa,CAAS;IAC9B,OAAO,CAAC,YAAY,CAAS;IAC7B,OAAO,CAAC,UAAU,CAAS;IAC3B,OAAO,CAAC,SAAS,CAAS;IAC1B,OAAO,CAAC,UAAU,CAAS;IAC3B,OAAO,CAAC,YAAY,CAAS;IAE7B,IAAW,MAAM,yBAEhB;IACD,IAAI;IAMJ,IAAW,SAAS,IAAI,OAAO,CAE9B;IAED,IAAW,QAAQ,IAAI,OAAO,CAE7B;IAED,IAAW,SAAS,IAAI,OAAO,CAE9B;IAED,IAAW,WAAW,IAAI,OAAO,CAEhC;IAEM,WAAW,CAAC,KAAK,EAAE,OAAO;IAIjC,IAAI,eAAe,IAGW,OAAO,CADpC;IACD,IAAI,eAAe,CAAC,QAAQ,EAAE,OAAO,EAIpC;IACD,MAAM;IAMN,MAAM;IAUN,gBAAgB;IAUhB,qBAAqB,CAAC,KAAK,EAAE,kBAAkB;IAW/C,mCAAmC;IAgDnC,qBAAqB,CAAC,KAAK,EAAE,kBAAkB;IAO/C,mBAAmB,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI;IAepD,oBAAoB;IAOpB,oBAAoB;IAepB,UAAU;IAUJ,cAAc,CAAC,IAAI,EAAE,MAAM;IAqD3B,cAAc,CAAC,IAAI,EAAE,MAAM;IAwB3B,sBAAsB,CAAC,MAAM,EAAE,MAAM;IAKrC,aAAa,CAAC,IAAI,EAAE,MAAM;IAwB1B,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG;IA8D5D,kBAAkB,CAAC,KAAK,EAAE,MAAM;IAShC,mBAAmB,CAAC,KAAK,EAAE,MAAM;IAKjC,SAAS;IAqBT,mBAAmB;IAInB,kBAAkB,CAAC,IAAI,EAAE,kBAAkB,EAAE,MAAM,CAAC,EAAE,GAAG;IAiBzD,gBAAgB;IAehB,iBAAiB,CAAC,IAAI,EAAE,GAAG;IAY3B,gBAAgB,CAAC,IAAI,EAAE,GAAG;IAc1B,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG;IAqB3C,YAAY;;;;;;;;;;;;IA2BZ,SAAS,CAAC,cAAc;IAOxB,cAAc;IAMd,aAAa;IAMb,cAAc;IAId,SAAS,CAAC,KAAK,EAAE,mBAAmB;IAKpC,OAAO,CAAC,KAAK,EAAE,mBAAmB;IAMlC,QAAQ,CAAC,SAAS,EAAE,MAAM;IAQ1B,0BAA0B,CAAC,IAAI,EAAE,MAAM;IAYvC,kBAAkB;CAKrB;AAED,eAAe,YAAY,CAAC"}