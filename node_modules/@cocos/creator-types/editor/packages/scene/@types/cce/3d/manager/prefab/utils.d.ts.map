{"version": 3, "file": "utils.d.ts", "sourceRoot": "", "sources": ["../../../../../source/script/3d/manager/prefab/utils.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,SAAS,EAAgC,IAAI,EAAE,MAAM,EAAW,KAAK,EAAgB,MAAM,IAAI,CAAC;AAGzG,KAAK,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAC3C,QAAA,MAAM,UAAU,iCAA2B,CAAC;AAG5C,KAAK,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;AACnD,QAAA,MAAM,cAAc,qCAA+B,CAAC;AAGpD,KAAK,oBAAoB,GAAG,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC;AAC/D,QAAA,MAAM,oBAAoB,2CAAqC,CAAC;AAGhE,KAAK,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC;AAC3D,QAAA,MAAM,kBAAkB,yCAAmC,CAAC;AAI5D,aAAK,WAAW;IACZ,UAAU,IAAI;IACd,WAAW,IAAI;IACf,cAAc,IAAI;IAClB,eAAe,IAAI;CACtB;AAwCD,cAAM,UAAU;IACZ,OAAc,WAAW,qBAAe;IACxC,OAAO,CAAC,mBAAmB,CAA4B;IACvD,OAAO,CAAC,0BAA0B,CAAoC;IAE/D,SAAS,CAAC,IAAI,EAAE,IAAI,GAAG,UAAU,GAAG,IAAI;IAKxC,mBAAmB,CAAC,IAAI,EAAE,IAAI;IAK9B,aAAa,CAAC,IAAI,EAAE,IAAI,GAAG,KAAK,EAAE,IAAI,GAAE,GAAQ;IAKhD,0BAA0B,CAAC,UAAU,EAAE,UAAU;IAuBjD,UAAU;IAKV,kCAAkC,CAAC,UAAU,EAAE,UAAU;IAMhE;;;;OAIG;IACI,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,UAAQ;IAczC,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,UAAQ,GAAG,IAAI,GAAC,SAAS,GAAC,IAAI;IAMtF,OAAO,CAAC,iBAAiB;IA2DlB,+BAA+B,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE;;;;;;IA6D9D,qBAAqB,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,GAAE,OAAO,GAAG,SAAqB;;;;IA+ExE,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,SAAS;IAIpE,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,KAAK,OAAO,GAAG,IAAI,EAAE,OAAO,UAAQ;IAI9F,wBAAwB,CAAC,IAAI,EAAE,SAAS;IAY/C;;;;OAIG;IACI,0BAA0B,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;;;;IAgClD,qBAAqB,CAAC,IAAI,EAAE,IAAI;IA0BhC,YAAY;IAIZ,oBAAoB;IAOpB,gBAAgB,CAAC,MAAM,EAAE,MAAM;IAM/B,0BAA0B,CAAC,QAAQ,EAAE,cAAc;IA0CnD,qBAAqB,CAAC,IAAI,EAAE,IAAI;IAevC,0BAA0B,CAAC,cAAc,EAAE,kBAAkB,EAAE,MAAM,EAAE,SAAS,GAAG,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE;IAWhJ,aAAa,CAAC,MAAM,EAAE,SAAS;;;;IAiCxB,4BAA4B,CAAC,UAAU,EAAE,UAAU,GAAG,SAAS,EAAE,MAAM,EAAE,IAAI,GAAG,SAAS;IAqBzF,oBAAoB,CAAC,UAAU,EAAE,UAAU,GAAG,SAAS,GAAG,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE;IA6BrG,mBAAmB,CAAC,eAAe,EAAE,kBAAkB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE;IAmBhG,iBAAiB,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE;IAoC/E,4BAA4B,CAAC,cAAc,EAAE,cAAc,EAAE,OAAO,EAAE,MAAM,EAAE;IAY9E,qBAAqB,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,GAAG,OAAO;IAW7F,mBAAmB,CAAC,cAAc,EAAE,cAAc,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;IA+BzF,sBAAsB,CAAC,cAAc,EAAE,cAAc,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;IAU5F,iCAAiC,CAAC,cAAc,EAAE,cAAc,EAAE,OAAO,EAAE,MAAM,EAAE;IAcnF,yBAAyB,CAAC,OAAO,EAAE,MAAM,EAAE;IAS3C,gCAAgC,CAAC,cAAc,EAAE,cAAc,EAAE,OAAO,EAAE,MAAM,EAAE;IAWlF,kCAAkC,CAAC,cAAc,EAAE,cAAc,EAAE,OAAO,EAAE,MAAM,EAAE;IAsBpF,mBAAmB,CAAC,cAAc,EAAE,cAAc,EAAE,OAAO,EAAE,MAAM,EAAE;IAcrE,sBAAsB,CAAC,cAAc,EAAE,cAAc,EAAE,OAAO,EAAE,MAAM,EAAE;IAW/E;;;OAGG;IACI,uBAAuB,CAAC,IAAI,EAAE,IAAI;IAelC,oBAAoB,CAAC,IAAI,EAAE,IAAI;IAgB/B,oBAAoB,CAAC,IAAI,EAAE,IAAI;IA+B/B,mBAAmB,CAAC,IAAI,EAAE,IAAI;IAgBrC;;;;OAIG;IACI,sBAAsB,CAAC,IAAI,EAAE,IAAI;IAejC,6BAA6B,CAAC,IAAI,EAAE,IAAI;IAU/C;;;;;;;;;OASG;IACI,4BAA4B,CAAC,IAAI,EAAE,IAAI;;;;IAwC9C,WAAW,CAAC,IAAI,EAAE,IAAI;IAQf,kBAAkB,CAAC,IAAI,EAAE,IAAI;;;;;;;;IAmE7B,cAAc,CAAC,UAAU,EAAE,IAAI,GAAG,SAAS;IAI3C,cAAc,CAAC,UAAU,EAAE,IAAI,GAAG,SAAS,EAAE,WAAW,EAAE,IAAI,GAAG,SAAS;IAYjF,OAAO,CAAC,gBAAgB;IASjB,kBAAkB,CAAC,SAAS,EAAE,SAAS;IAsBvC,oBAAoB,CAAC,IAAI,EAAE,IAAI;IAmD/B,2BAA2B,CAAC,MAAM,EAAE,IAAI,GAAG,SAAS,EAAE,IAAI,EAAE,IAAI,GAAG,KAAK,GAAG,IAAI;IAU/E,8BAA8B,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE;IAkCvE,yBAAyB,CAAC,QAAQ,EAAE,IAAI,GAAG,KAAK;IAmDzC,UAAU,CAAC,IAAI,EAAE,MAAM;IAIvB,gBAAgB,CAAC,IAAI,EAAE,IAAI;IAiB3B,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO;IAqDrD,yBAAyB,CAAC,QAAQ,EAAE,IAAI,GAAG,KAAK;IAShD,wBAAwB,CAAC,IAAI,EAAE,IAAI,GAAG,KAAK;IAmBlD;;;;;OAKG;IACI,sCAAsC,CAAC,IAAI,EAAE,IAAI;IAoBxD;;;OAGG;IACI,8BAA8B,CAAC,IAAI,EAAE,IAAI;IA0BhD;;;;OAIG;IACI,4BAA4B,CAAC,IAAI,EAAE,IAAI;IA4D9C;;;OAGG;IACI,uBAAuB,CAAC,IAAI,EAAE,IAAI;IAezC;;;;OAIG;IACI,sCAAsC,CAAC,IAAI,EAAE,IAAI;CAiC3D;AAED,QAAA,MAAM,WAAW,YAAmB,CAAC;AAErC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC"}