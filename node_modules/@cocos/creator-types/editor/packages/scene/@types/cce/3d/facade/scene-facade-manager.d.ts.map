{"version": 3, "file": "scene-facade-manager.d.ts", "sourceRoot": "", "sources": ["../../../../source/script/3d/facade/scene-facade-manager.ts"], "names": [], "mappings": "AAAA,OAAO,EACH,kBAAkB,EAClB,gBAAgB,EAChB,kBAAkB,EAClB,cAAc,EACd,gBAAgB,EAChB,iBAAiB,EACjB,iBAAiB,EACjB,sBAAsB,EACtB,sBAAsB,EACtB,6BAA6B,EAC7B,cAAc,EACd,+BAA+B,EAC/B,YAAY,EACZ,mBAAmB,EACtB,MAAM,2BAA2B,CAAC;AACnC,OAAO,EAAE,yBAAyB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAEhI,OAAO,YAAY,MAAM,2CAA2C,CAAC;AAYrE,OAAO,KAAK,WAAW,MAAM,yBAAyB,CAAC;AAKvD,OAAO,EAAE,IAAI,EAAE,SAAS,EAA4C,MAAM,IAAI,CAAC;AAoB/E,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AAIzB,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,EAAE,UAAU,EAAE,MAAM,6DAA6D,CAAC;AAEzF,cAAM,kBAAmB,YAAW,YAAY;IAC5C,OAAO,CAAC,YAAY,CAAqB;IACzC,OAAO,CAAC,YAAY,CAAS;IAC7B,OAAO,CAAC,UAAU,CAAkB;IAEvB,IAAI;IAWjB,iBAAiB,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI;IAInC,iBAAiB,CAAC,IAAI,EAAE,OAAO;IAKzB,oBAAoB,CAAC,KAAK,EAAE,MAAM;IAclC,WAAW;IAgEjB,iBAAiB;IAyCjB;;;OAGG;IACH,OAAO,CAAC,gCAAgC;IAkBxC;;;OAGG;IACH,iBAAiB;IASJ,SAAS,CAAC,IAAI,EAAE,MAAM;IAkDnC,OAAO,CAAC,KAAK;IAKA,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC;IA0B/B,UAAU,IAAI,OAAO,CAAC,OAAO,CAAC;IAI9B,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC;IAK7B,eAAe;IAOrB,aAAa,CAAC,KAAK,EAAE,GAAG;IAOxB,aAAa,CAAC,KAAK,EAAE,GAAG;IAMxB,aAAa,CAAC,KAAK,EAAE,GAAG;IAMxB,YAAY,CAAC,KAAK,EAAE,GAAG;IAGjB,gBAAgB,CAAC,IAAI,EAAE,GAAG,EAAE;IAI5B,SAAS,CAAC,KAAK,UAAQ;IAMvB,eAAe,CAAC,IAAI,CAAC,EAAE,GAAG;IAK1B,WAAW;IAKX,aAAa,CAAC,IAAI,EAAE,MAAM;IAI1B,qBAAqB,CAAC,IAAI,EAAE,MAAM;IAIxC,mBAAmB;IAIb,wBAAwB,IAAI,OAAO,CAAC,MAAM,CAAC;IAI3C,eAAe;IAIf,YAAY,CAAC,OAAO,CAAC,EAAE,mBAAmB;IAI1C,eAAe;IAIf,uBAAuB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAIvD,iBAAiB;IAIjB,wBAAwB;IAI9B,SAAS;IAIT,qBAAqB;IAIrB,gBAAgB,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI;IAI3C,qBAAqB,CAAC,IAAI,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI;IAIrD,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS;IAUrB,aAAa,CAAC,IAAI,EAAE,MAAM;IAmC1B,eAAe,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC;IAI9D,SAAS,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAIzC,iBAAiB,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC;IAIhE,sBAAsB,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC;IAIrE,4BAA4B,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC;IAI3E,0BAA0B,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC;IAI/E,uBAAuB,CAAC,OAAO,EAAE,kBAAkB;IAInD,oBAAoB,CAAC,OAAO,EAAE,gBAAgB;IAI9C,sBAAsB,CAAC,OAAO,EAAE,kBAAkB;IAI5C,yBAAyB,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAInF,cAAc,IAAI,IAAI;IAItB,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE;IAa5C,aAAa,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE;IAIxD,8BAA8B;IAI9B,2BAA2B;IAIpB,OAAO,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE;IAY1B,SAAS,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAOvD,aAAa,CAAC,OAAO,EAAE,cAAc,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAIzD,UAAU,CAAC,OAAO,EAAE,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC;IAO7D,UAAU,CAAC,OAAO,EAAE,iBAAiB;IAM/B,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO;IAI3D,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAItE,mBAAmB,CAAC,IAAI,EAAE,IAAI;IAQ9B,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,kBAAkB;IAgBlD,iBAAiB,CAAC,IAAI,EAAE,IAAI;IAI5B,SAAS,CAAC,IAAI,EAAE,IAAI;IAMpB,YAAY,CAAC,IAAI,EAAE,IAAI;IAMvB,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,GAAE,GAAQ;IAQtC,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,GAAE,GAAQ;IAWlC,cAAc,CAAC,IAAI,EAAE,MAAM;IAI3B,4BAA4B,CAAC,IAAI,EAAE,MAAM;IAI/C,eAAe,CAAC,OAAO,EAAE,sBAAsB;IAI/C,cAAc,CAAC,IAAI,EAAE,MAAM;IAI3B,eAAe,CAAC,OAAO,EAAE,sBAAsB;IAIzC,sBAAsB,CAAC,OAAO,EAAE,6BAA6B,GAAG,OAAO,CAAC,GAAG,CAAC;IAI5E,wBAAwB,CAAC,OAAO,EAAE,+BAA+B,GAAG,OAAO,CAAC,GAAG,CAAC;IAItF,cAAc,CAAC,IAAI,EAAE,SAAS;IAM9B,iBAAiB,CAAC,IAAI,EAAE,SAAS;IAMjC,gBAAgB,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,GAAE,GAAQ;IAQhD,kBAAkB,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,GAAE,GAAQ;IAW5C,QAAQ,CAAC,OAAO,CAAC,EAAE,GAAG;IAMtB,aAAa;IAKnB,cAAc,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,EAAE,OAAO,CAAC,EAAE,iBAAiB,GAAG,kBAAkB;IAIzF,eAAe,CAAC,SAAS,EAAE,kBAAkB,GAAG,OAAO;IAIvD,YAAY,CAAC,SAAS,EAAE,kBAAkB,GAAG,OAAO;IAI9C,IAAI;IAKJ,IAAI;IAKjB;;;OAGG;IACI,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,UAAO;IAOpC,SAAS,IAAI,IAAI;IAIlB,WAAW;IAQJ,eAAe,IAAI,OAAO,CAAC,GAAG,CAAC;IAI/B,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAIzC,WAAW,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAI7C,mBAAmB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAI/C,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE;QAAE,IAAI,CAAC,EAAE,OAAO,CAAA;KAAE;IAI5E,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG;IAIvC,qBAAqB,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAI9C,oBAAoB,CAAC,IAAI,EAAE,MAAM;IAIjC,2BAA2B,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAIpD,0BAA0B,CAAC,IAAI,EAAE,MAAM;IAIvC,mBAAmB,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAI5C,kBAAkB,CAAC,IAAI,EAAE,MAAM;IAI/B,kBAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG;IAI9C,oBAAoB,CAAC,IAAI,EAAE,GAAG;IAI9B,mBAAmB,CAAC,IAAI,EAAE,MAAM,EAAE,kBAAkB,EAAE,GAAG;IAI/D,oBAAoB,CAAC,IAAI,EAAE,MAAM;IAIjC,0BAA0B,CAAC,IAAI,EAAE,MAAM;IAIvC,kBAAkB,CAAC,IAAI,EAAE,MAAM;IAI/B,wBAAwB;IAIlB,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG;IAsBtD,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;IAKnC,YAAY,CAAC,IAAI,EAAE,MAAM;IAKzB,YAAY,CAAC,KAAK,EAAE,MAAM;IAQpB,kBAAkB;IAIxB,kBAAkB,IAAI,MAAM;IAI5B,eAAe,IAAI,OAAO;IAI1B,kBAAkB;IAIZ,eAAe,IAAI,OAAO,CAAC,MAAM,CAAC;IAIlC,oBAAoB,IAAI,OAAO,CAAC,MAAM,CAAC;IAIvC,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC;IAInC,kBAAkB;IAIlB,kBAAkB;IAIlB,sBAAsB;IAItB,oBAAoB,CAAC,IAAI,EAAE,MAAM;IAIjC,QAAQ,CAAC,IAAI,EAAE,MAAM;IAIrB,aAAa,CAAC,IAAI,EAAE,MAAM;IAI1B,OAAO,CAAC,KAAK,EAAE,OAAO;IAItB,cAAc,CAAC,IAAI,EAAE,OAAO;IAI5B,gBAAgB,CAAC,IAAI,EAAE,MAAM;IAI7B,oBAAoB,CAAC,YAAY,EAAE,OAAO;IAI1C,yBAAyB;IAIzB,uBAAuB,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;IAKhD,wBAAwB;IAIxB,sBAAsB,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG;IAO5C,KAAK,CAAC,IAAI,GAAE,MAAM,EAAE,GAAG,IAAW,EAAE,gBAAgB,CAAC,EAAE,gBAAgB,EAAE,SAAS,CAAC,EAAE,OAAO,GAAG,IAAI;IAInG,oBAAoB,CAAC,KAAK,EAAE,MAAM,EAAE;IAIpC,kBAAkB;IAIlB,cAAc,CAAC,OAAO,EAAE,OAAO;IAI/B,aAAa;IAIb,iBAAiB;IAIjB,gBAAgB,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI;IAIvC,iBAAiB,IAAI,GAAG;IAGxB,iBAAiB,CAAC,IAAI,EAAE,GAAG;IAG3B,mBAAmB;IAInB,mBAAmB,IAAI,MAAM;IAG7B,mBAAmB,CAAC,KAAK,EAAE,MAAM;IAIjC,oBAAoB,IAAI,MAAM;IAG9B,oBAAoB,CAAC,KAAK,EAAE,MAAM;IAGlC,2BAA2B,CAAC,MAAM,EAAE,OAAO;IAG3C,2BAA2B;IAI3B,eAAe,IAAI,IAAI;IAGvB,iBAAiB,IAAI,IAAI;IAGzB,kBAAkB,IAAI,IAAI;IAI1B,wBAAwB,IAAI,IAAI;IAIhC,0BAA0B,IAAI,IAAI;IAIlC,oBAAoB,IAAI,IAAI;IAO5B,0BAA0B,IAAI,GAAG;IAGjC,yBAAyB,IAAI,GAAG;IAGhC,sBAAsB,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;IAG5C,sBAAsB,CAAC,IAAI,EAAE,MAAM,GAAG,GAAG;IAGzC,sBAAsB,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,GAAG;IAG/D,wBAAwB,CAAC,IAAI,EAAE,MAAM,GAAG,GAAG;IAG3C,uBAAuB,CAAC,QAAQ,EAAE,MAAM,GAAG,GAAG;IAG9C,6BAA6B,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM;IAGvD,8BAA8B,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,GAAG;IAGvG,yBAAyB,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,GAAG;IAGvE,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAWnF,uBAAuB,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAGzE,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAG9C,eAAe,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAGpE,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAG/C,QAAQ,IAAI,OAAO,CAAC,OAAO,CAAC;IAG5B,uBAAuB,CAAC,aAAa,EAAE,cAAc,EAAE,EAAE,OAAO,CAAC,EAAE,yBAAyB;IAGlG,0BAA0B,CAAC,IAAI,EAAE,MAAM;IAIvC,qBAAqB,CAAC,IAAI,EAAE,MAAM;IASzC,SAAS,CAAC,wBAAwB;IAWrB,eAAe,CAAC,IAAI,EAAE,MAAM;IAI5B,cAAc,CAAC,IAAI,EAAE,MAAM;IAI3B,UAAU,CAAC,IAAI,EAAE,MAAM;IAIvB,YAAY,CAAC,IAAI,EAAE,UAAU;IAI7B,YAAY,CAAC,IAAI,EAAE,UAAU;IAI7B,uBAAuB;IAU7B,cAAc,IAAI,MAAM,EAAE;IAG1B,YAAY,CAAC,IAAI,EAAE,MAAM;IAGzB,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAG9B,YAAY,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAGhC,cAAc,IAAI,IAAI;IAOtB,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI;IAItC,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE;IAI7B,YAAY,CAAC,IAAI,EAAE,MAAM;IAOzB,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;IAOjC,YAAY,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM;IAG5C,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,GAAG;IAGhC,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,GAAG;IAGpD,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,GAAG,GAAG;IAGpD,WAAW,CAAC,QAAQ,EAAE,MAAM;IAGlC,qBAAqB,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAGtD,sBAAsB,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAOvD,qBAAqB,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAGzC,gBAAgB,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAOpC,qBAAqB,CAAC,IAAI,EAAE,MAAM,GAAG,GAAG;IAGxC,oBAAoB,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;IAGvD;;;OAGG;IACI,YAAY;IAGnB;;;OAGG;IACI,eAAe;IAGtB;;;OAGG;IACI,aAAa;IAGpB;;;OAGG;IACI,YAAY;IAMZ,sBAAsB,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,IAAI;IAG7D,SAAS,CAAC,YAAY,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,UAAU,CAAC,OAAO,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI;IAG9F,QAAQ,CAAC,YAAY,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,UAAU,CAAC,OAAO,IAAI,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI;IAmBtF,sBAAsB,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,SAAK;IASrD,qBAAqB,CAAC,CAAC,SAAS,MAAM,OAAO,WAAW,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,EAAE,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC;IAU5H,kBAAkB,IAAI,IAAI;IAKnB,cAAc;IAGd,oBAAoB,CAAC,IAAI,EAAE,OAAO;IAoB5B,sBAAsB,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,OAAO;IA0CxE,yBAAyB,CAAC,IAAI,EAAE,MAAM;IAOhC,mBAAmB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAK/C,QAAQ,CAAC,QAAQ,EAAE,YAAY;IAIrC,eAAe,CAAC,MAAM,EAAE,OAAO;IAI/B,iBAAiB;IAQjB,sBAAsB,CAAC,OAAO,EAAE,OAAO;IAIjC,gBAAgB,CAAC,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;IAK/C,mBAAmB,CAAC,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE;IAItE,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,KAAK,GAAE,MAAM,EAAO;IAK1D,gBAAgB;IAIvB;;;;SAIK;IACQ,iBAAiB,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;IA6B/C,iBAAiB;IAIX,eAAe;IAMf,0BAA0B;IAMvC,OAAO,CAAC,uBAAuB;IAK/B,wBAAwB,CAAC,IAAI,EAAE,OAAO,GAAG,OAAO;IAIhD,uBAAuB,IAAI,OAAO;IAIlC,mCAAmC,CAAC,IAAI,EAAE,OAAO,GAAG,OAAO;IAI3D,kCAAkC,IAAI,OAAO;IAK7C,oBAAoB,CAAC,QAAQ,EAAE,MAAM;CAGxC;AAED,OAAO,EAAE,kBAAkB,EAAE,CAAC"}