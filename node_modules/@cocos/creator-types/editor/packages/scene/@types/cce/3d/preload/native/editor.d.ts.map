{"version": 3, "file": "editor.d.ts", "sourceRoot": "", "sources": ["../../../../../source/script/3d/preload/native/editor.ts"], "names": [], "mappings": ";AAEA,cAAM,WAAW;IACA,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,mBAAmB;IAIjF,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,mBAAmB;IAI7F,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,mBAAmB;IAIlF,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,mBAAmB;CAG9G;AAED,cAAM,YAAY;IACP,KAAK,CAAC,QAAQ,EAAE,MAAM;CAGhC;AAED,cAAM,WAAW;IACA,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE;IAIvD,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE;IAIpD,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE;CAGzD;AAED,cAAM,WAAW;IACA,WAAW,CAAC,IAAI,EAAE,GAAG;CAGrC;AAED,cAAM,aAAa;IACF,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM;IAInC,eAAe;IAIf,WAAW,CAAC,IAAI,EAAE,MAAM;CAGxC;AAED,cAAM,UAAU;IACC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,oBAAoB,EAAE,MAAM,CAAC,EAAE,GAAG;IAIhF,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,oBAAoB,EAAE,MAAM,CAAC,EAAE,GAAG;IAIhF,IAAI,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,mBAAmB,EAAE,MAAM,CAAC,EAAE,GAAG;CAG9E;AAED,cAAM,WAAW;IACN,cAAc,CAAC,OAAO,EAAE,MAAM;IAI9B,YAAY,CAAC,OAAO,EAAE,MAAM;CAGtC;AAED,cAAM,QAAQ;IACG,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG;CAGvC;AAED,cAAM,SAAS;IACE,IAAI,CAAC,IAAI,EAAE,MAAM;CAGjC;AAED,cAAM,OAAO;IACT,IAAI,SAAM;CACb;AAED,cAAM,WAAW;IACb,IAAI,SAAM;CACb;AAED,cAAM,UAAU;IACL,OAAO,EAAE,WAAW,CAAqB;IACzC,QAAQ,EAAE,YAAY,CAAsB;IAC5C,OAAO,EAAE,WAAW,CAAoB;IACxC,OAAO,EAAE,WAAW,CAAqB;IACzC,SAAS,EAAE,aAAa,CAAuB;IAC/C,MAAM,EAAE,UAAU,CAAoB;IACtC,IAAI,EAAE,QAAQ,CAAkB;IAChC,KAAK,EAAE,SAAS,CAAmB;IACnC,GAAG,EAAE,OAAO,CAAiB;IAC7B,OAAO,EAAE,WAAW,CAAqB;IACzC,OAAO,EAAE,WAAW,CAAqB;CACnD;AAED,OAAO,EAAE,UAAU,EAAE,CAAC"}