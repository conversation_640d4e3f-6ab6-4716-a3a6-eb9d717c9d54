/// <reference path="../../../../../../../../resources/3d/engine/bin/.declarations/cc.d.ts" />
/// <reference path="../../../public/gizmos/utils/engine/3d.d.ts" />
/// <reference path="../../../public/gizmos/manager/data.d.ts" />
/// <reference path="../asset/asset-watcher.d.ts" />
/// <reference types="@cocos/creator-types/engine/cc" />
import { Node } from 'cc';
export declare function returnFalseWithLog(message: string): boolean;
export declare function getPrefabInfo(node: Node): import("cc").Prefab._utils.PrefabInfo | null;
//# sourceMappingURL=common.d.ts.map